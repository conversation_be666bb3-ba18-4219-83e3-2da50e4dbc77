.avue-card {
  &__item {
    margin-bottom: 16px;
    border: 1px solid #e8e8e8;
    background-color: #fff;
    box-sizing: border-box;
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-variant: tabular-nums;
    line-height: 1.5;
    list-style: none;
    font-feature-settings: 'tnum';
    cursor: pointer;
    height: 200px;

    &:hover {
      border-color: rgba(0, 0, 0, 0.09);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
    }

    &--add {
      border: 1px dashed #000;
      width: 100%;
      color: rgba(0, 0, 0, 0.45);
      background-color: #fff;
      border-color: #d9d9d9;
      border-radius: 2px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;

      i {
        margin-right: 10px;
      }

      &:hover {
        color: #40a9ff;
        background-color: #fff;
        border-color: #40a9ff;
      }
    }
  }

  &__body {
    display: flex;
    padding: 24px;
  }

  &__detail {
    flex: 1;
  }

  &__avatar {
    width: 48px;
    height: 48px;
    border-radius: 48px;
    overflow: hidden;
    margin-right: 12px;

    img {
      width: 100%;
      height: 100%;
    }
  }

  &__title {
    color: rgba(0, 0, 0, 0.85);
    margin-bottom: 12px;
    font-size: 16px;

    &:hover {
      color: #1890ff;
    }
  }

  &__info {
    color: rgba(0, 0, 0, 0.45);
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    height: 64px;
  }

  &__menu {
    display: flex;
    justify-content: space-around;
    height: 50px;
    background: #f7f9fa;
    color: rgba(0, 0, 0, 0.45);
    text-align: center;
    line-height: 50px;

    &:hover {
      color: #1890ff;
    }
  }
}

/** joolun 额外加的 */
.avue-comment__main {
  flex: unset !important;
  border-radius: 5px !important;
  margin: 0 8px !important;
}

.avue-comment__header {
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
}

.avue-comment__body {
  border-bottom-right-radius: 5px;
  border-bottom-left-radius: 5px;
}
