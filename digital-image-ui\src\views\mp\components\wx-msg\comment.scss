/* 来自 https://github.com/nmxiaowei/avue/blob/master/styles/src/element-ui/comment.scss  */
.avue-comment {
  margin-bottom: 30px;
  display: flex;
  align-items: flex-start;

  &--reverse {
    flex-direction: row-reverse;

    .avue-comment__main {
      &:before,
      &:after {
        left: auto;
        right: -8px;
        border-width: 8px 0 8px 8px;
      }

      &:before {
        border-left-color: #dedede;
      }

      &:after {
        border-left-color: #f8f8f8;
        margin-right: 1px;
        margin-left: auto;
      }
    }
  }

  &__avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    border: 1px solid transparent;
    box-sizing: border-box;
    vertical-align: middle;
  }

  &__header {
    padding: 5px 15px;
    background: #f8f8f8;
    border-bottom: 1px solid #eee;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  &__author {
    font-weight: 700;
    font-size: 14px;
    color: #999;
  }

  &__main {
    flex: 1;
    margin: 0 20px;
    position: relative;
    border: 1px solid #dedede;
    border-radius: 2px;

    &:before,
    &:after {
      position: absolute;
      top: 10px;
      left: -8px;
      right: 100%;
      width: 0;
      height: 0;
      display: block;
      content: ' ';
      border-color: transparent;
      border-style: solid solid outset;
      border-width: 8px 8px 8px 0;
      pointer-events: none;
    }

    &:before {
      border-right-color: #dedede;
      z-index: 1;
    }

    &:after {
      border-right-color: #f8f8f8;
      margin-left: 1px;
      z-index: 2;
    }
  }

  &__body {
    padding: 15px;
    overflow: hidden;
    background: #fff;
    font-family:
      Segoe UI,
      Lucida Grande,
      Helvetica,
      Arial,
      Microsoft YaHei,
      FreeSans,
      Arimo,
      Droid Sans,
      wenquanyi micro hei,
      Hiragino Sans GB,
      Hiragino Sans GB W3,
      FontAwesome,
      sans-serif;
    color: #333;
    font-size: 14px;
  }

  blockquote {
    margin: 0;
    font-family:
      Georgia,
      Times New Roman,
      Times,
      Kai,
      Kaiti SC,
      KaiTi,
      BiauKai,
      FontAwesome,
      serif;
    padding: 1px 0 1px 15px;
    border-left: 4px solid #ddd;
  }
}
