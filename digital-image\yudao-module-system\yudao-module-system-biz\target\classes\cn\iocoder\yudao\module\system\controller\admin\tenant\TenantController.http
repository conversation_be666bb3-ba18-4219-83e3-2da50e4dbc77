### 获取租户编号 /admin-api/system/get-id-by-name
GET {{baseUrl}}/system/tenant/get-id-by-name?name=xl

### 创建租户 /admin-api/system/tenant/create
POST {{baseUrl}}/system/tenant/create
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}

{
  "name": "后台",
  "contactName": "芋艿",
  "contactMobile": "***********",
  "status": 0,
  "domain": "https://www.chanchat.cn",
  "packageId": 110,
  "expireTime": *************,
  "accountCount": 20,
  "username": "admin",
  "password": "123321"
}
