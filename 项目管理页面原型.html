<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目管理 - 数字画像系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #409eff;
            margin-bottom: 10px;
        }
        
        .breadcrumb {
            color: #666;
            font-size: 14px;
        }
        
        .search-form {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
            align-items: center;
        }
        
        .form-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .form-item label {
            min-width: 80px;
            font-weight: 500;
        }
        
        .form-item input, .form-item select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-primary {
            background: #409eff;
            color: white;
        }
        
        .btn-default {
            background: #f4f4f5;
            color: #606266;
        }
        
        .btn-success {
            background: #67c23a;
            color: white;
        }
        
        .btn-danger {
            background: #f56c6c;
            color: white;
        }
        
        .btn-warning {
            background: #e6a23c;
            color: white;
        }
        
        .btn-info {
            background: #909399;
            color: white;
        }
        
        .btn:hover {
            opacity: 0.8;
        }
        
        .toolbar {
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .table-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        tr:hover {
            background: #f5f7fa;
        }
        
        .status-tag {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-success {
            background: #f0f9ff;
            color: #67c23a;
            border: 1px solid #b3e19d;
        }
        
        .status-warning {
            background: #fdf6ec;
            color: #e6a23c;
            border: 1px solid #f5dab1;
        }
        
        .status-danger {
            background: #fef0f0;
            color: #f56c6c;
            border: 1px solid #fbc4c4;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            gap: 10px;
        }
        
        .page-btn {
            padding: 6px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }
        
        .page-btn.active {
            background: #409eff;
            color: white;
            border-color: #409eff;
        }
        
        .checkbox {
            width: 16px;
            height: 16px;
        }
        
        .action-buttons {
            display: flex;
            gap: 5px;
        }
        
        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <h1>项目管理</h1>
            <div class="breadcrumb">系统管理 > 项目管理 > 项目列表</div>
        </div>
        
        <!-- 搜索表单 -->
        <div class="search-form">
            <div class="form-row">
                <div class="form-item">
                    <label>项目名称:</label>
                    <input type="text" placeholder="请输入项目名称">
                </div>
                <div class="form-item">
                    <label>项目类型:</label>
                    <select>
                        <option value="">请选择项目类型</option>
                        <option value="1">科研项目</option>
                        <option value="2">教学项目</option>
                        <option value="3">产学研项目</option>
                    </select>
                </div>
                <div class="form-item">
                    <label>项目级别:</label>
                    <select>
                        <option value="">请选择项目级别</option>
                        <option value="1">国家级</option>
                        <option value="2">省级</option>
                        <option value="3">校级</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-item">
                    <label>项目状态:</label>
                    <select>
                        <option value="">请选择项目状态</option>
                        <option value="1">立项</option>
                        <option value="2">进行中</option>
                        <option value="3">结项</option>
                    </select>
                </div>
                <div class="form-item">
                    <button class="btn btn-primary">查询</button>
                    <button class="btn btn-default">重置</button>
                </div>
            </div>
        </div>
        
        <!-- 工具栏 -->
        <div class="toolbar">
            <button class="btn btn-success">新增项目</button>
            <button class="btn btn-danger">批量删除</button>
            <button class="btn btn-info">导出数据</button>
        </div>
        
        <!-- 数据表格 -->
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th><input type="checkbox" class="checkbox"></th>
                        <th>项目名称</th>
                        <th>项目类型</th>
                        <th>项目级别</th>
                        <th>负责人</th>
                        <th>立项时间</th>
                        <th>项目状态</th>
                        <th>创建时间</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><input type="checkbox" class="checkbox"></td>
                        <td>基于深度学习的图像识别技术研究</td>
                        <td>科研项目</td>
                        <td>国家级</td>
                        <td>张教授</td>
                        <td>2023-01-15</td>
                        <td><span class="status-tag status-success">进行中</span></td>
                        <td>2023-01-10 14:30:25</td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-primary btn-small">编辑</button>
                                <button class="btn btn-info btn-small">详情</button>
                                <button class="btn btn-danger btn-small">删除</button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td><input type="checkbox" class="checkbox"></td>
                        <td>新工科背景下的计算机教学改革研究</td>
                        <td>教学项目</td>
                        <td>省级</td>
                        <td>李教授</td>
                        <td>2023-03-20</td>
                        <td><span class="status-tag status-warning">立项</span></td>
                        <td>2023-03-15 09:15:42</td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-primary btn-small">编辑</button>
                                <button class="btn btn-info btn-small">详情</button>
                                <button class="btn btn-danger btn-small">删除</button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td><input type="checkbox" class="checkbox"></td>
                        <td>智能制造产学研合作项目</td>
                        <td>产学研项目</td>
                        <td>校级</td>
                        <td>王教授</td>
                        <td>2022-09-10</td>
                        <td><span class="status-tag status-danger">结项</span></td>
                        <td>2022-09-05 16:45:18</td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-primary btn-small">编辑</button>
                                <button class="btn btn-info btn-small">详情</button>
                                <button class="btn btn-danger btn-small">删除</button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
            
            <!-- 分页 -->
            <div class="pagination">
                <button class="page-btn">上一页</button>
                <button class="page-btn active">1</button>
                <button class="page-btn">2</button>
                <button class="page-btn">3</button>
                <button class="page-btn">下一页</button>
                <span style="margin-left: 20px;">共 25 条记录，每页 10 条</span>
            </div>
        </div>
    </div>
</body>
</html>
