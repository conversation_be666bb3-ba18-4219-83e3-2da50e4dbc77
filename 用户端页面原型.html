<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心 - 数字画像系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(45deg, #409eff, #67c23a);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
            font-weight: bold;
        }
        
        .user-details h2 {
            color: #409eff;
            margin-bottom: 8px;
        }
        
        .user-meta {
            color: #666;
            font-size: 14px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            text-align: center;
            transition: transform 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-5px);
        }
        
        .stat-icon {
            width: 60px;
            height: 60px;
            margin: 0 auto 15px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
        }
        
        .stat-icon.projects {
            background: linear-gradient(45deg, #409eff, #36cfc9);
        }
        
        .stat-icon.competitions {
            background: linear-gradient(45deg, #f56c6c, #ff9a9e);
        }
        
        .stat-icon.awards {
            background: linear-gradient(45deg, #e6a23c, #ffd93d);
        }
        
        .stat-icon.points {
            background: linear-gradient(45deg, #67c23a, #95de64);
        }
        
        .stat-value {
            font-size: 32px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        
        .content-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
        }
        
        .main-content {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        
        .card-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        
        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }
        
        .view-more {
            color: #409eff;
            text-decoration: none;
            font-size: 14px;
        }
        
        .view-more:hover {
            text-decoration: underline;
        }
        
        .project-item, .competition-item {
            padding: 15px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .project-item:last-child, .competition-item:last-child {
            border-bottom: none;
        }
        
        .item-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .item-meta {
            display: flex;
            gap: 15px;
            font-size: 14px;
            color: #666;
        }
        
        .tag {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .tag-primary {
            background: #e6f7ff;
            color: #1890ff;
        }
        
        .tag-success {
            background: #f6ffed;
            color: #52c41a;
        }
        
        .tag-warning {
            background: #fff7e6;
            color: #fa8c16;
        }
        
        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        
        .quick-actions {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        
        .action-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            text-align: center;
            display: block;
        }
        
        .action-btn-primary {
            background: linear-gradient(45deg, #409eff, #36cfc9);
            color: white;
        }
        
        .action-btn-success {
            background: linear-gradient(45deg, #67c23a, #95de64);
            color: white;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .recent-activity {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .activity-item {
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #409eff;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-text {
            font-size: 14px;
            color: #333;
            margin-bottom: 4px;
        }
        
        .activity-time {
            font-size: 12px;
            color: #999;
        }
        
        @media (max-width: 768px) {
            .content-grid {
                grid-template-columns: 1fr;
            }
            
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 用户信息头部 -->
        <div class="header">
            <div class="user-info">
                <div class="avatar">张</div>
                <div class="user-details">
                    <h2>张教授</h2>
                    <div class="user-meta">
                        <span>计算机学院 | 教授 | 博士生导师</span><br>
                        <span>最后登录：2024-01-15 14:30:25</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon projects">📊</div>
                <div class="stat-value">15</div>
                <div class="stat-label">参与项目</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon competitions">🏆</div>
                <div class="stat-value">8</div>
                <div class="stat-label">指导竞赛</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon awards">🥇</div>
                <div class="stat-value">12</div>
                <div class="stat-label">获奖数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon points">⭐</div>
                <div class="stat-value">2580</div>
                <div class="stat-label">积分总数</div>
            </div>
        </div>
        
        <!-- 主要内容区域 -->
        <div class="content-grid">
            <div class="main-content">
                <!-- 最近项目 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">最近项目</h3>
                        <a href="#" class="view-more">查看全部</a>
                    </div>
                    <div class="project-list">
                        <div class="project-item">
                            <div class="item-title">基于深度学习的图像识别技术研究</div>
                            <div class="item-meta">
                                <span class="tag tag-primary">国家级</span>
                                <span class="tag tag-success">进行中</span>
                                <span>立项时间：2023-01-15</span>
                            </div>
                        </div>
                        <div class="project-item">
                            <div class="item-title">新工科背景下的计算机教学改革研究</div>
                            <div class="item-meta">
                                <span class="tag tag-warning">省级</span>
                                <span class="tag tag-primary">立项</span>
                                <span>立项时间：2023-03-20</span>
                            </div>
                        </div>
                        <div class="project-item">
                            <div class="item-title">智能制造产学研合作项目</div>
                            <div class="item-meta">
                                <span class="tag tag-success">校级</span>
                                <span class="tag tag-warning">结项</span>
                                <span>立项时间：2022-09-10</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 最近竞赛 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">最近竞赛</h3>
                        <a href="#" class="view-more">查看全部</a>
                    </div>
                    <div class="competition-list">
                        <div class="competition-item">
                            <div class="item-title">全国大学生数学建模竞赛</div>
                            <div class="item-meta">
                                <span class="tag tag-primary">国家级</span>
                                <span class="tag tag-warning">一等奖</span>
                                <span>获奖时间：2023-11-15</span>
                            </div>
                        </div>
                        <div class="competition-item">
                            <div class="item-title">省大学生程序设计竞赛</div>
                            <div class="item-meta">
                                <span class="tag tag-success">省级</span>
                                <span class="tag tag-primary">二等奖</span>
                                <span>获奖时间：2023-10-20</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 侧边栏 -->
            <div class="sidebar">
                <!-- 快捷操作 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">快捷操作</h3>
                    </div>
                    <div class="quick-actions">
                        <a href="#" class="action-btn action-btn-primary">新增项目</a>
                        <a href="#" class="action-btn action-btn-success">新增竞赛</a>
                        <a href="#" class="action-btn action-btn-primary">个人设置</a>
                        <a href="#" class="action-btn action-btn-success">数据导出</a>
                    </div>
                </div>
                
                <!-- 最近活动 -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">最近活动</h3>
                    </div>
                    <div class="recent-activity">
                        <div class="activity-item">
                            <div class="activity-icon">📝</div>
                            <div class="activity-content">
                                <div class="activity-text">创建了新项目"AI技术研究"</div>
                                <div class="activity-time">2小时前</div>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-icon">🏆</div>
                            <div class="activity-content">
                                <div class="activity-text">更新了竞赛获奖信息</div>
                                <div class="activity-time">1天前</div>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-icon">📊</div>
                            <div class="activity-content">
                                <div class="activity-text">查看了项目统计报告</div>
                                <div class="activity-time">3天前</div>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-icon">⚙️</div>
                            <div class="activity-content">
                                <div class="activity-text">修改了个人信息</div>
                                <div class="activity-time">1周前</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
