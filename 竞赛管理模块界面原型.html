<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>竞赛管理模块 - 数字画像系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }
        
        .container {
            display: flex;
            min-height: 100vh;
        }
        
        /* 侧边栏样式 */
        .sidebar {
            width: 240px;
            background: #2c3e50;
            color: white;
            padding: 20px 0;
        }
        
        .logo {
            text-align: center;
            padding: 20px;
            border-bottom: 1px solid #34495e;
            margin-bottom: 20px;
        }
        
        .logo h2 {
            color: #3498db;
        }
        
        .menu-item {
            padding: 12px 20px;
            cursor: pointer;
            transition: background 0.3s;
            border-left: 3px solid transparent;
        }
        
        .menu-item:hover {
            background: #34495e;
        }
        
        .menu-item.active {
            background: #3498db;
            border-left-color: #2980b9;
        }
        
        .menu-item i {
            margin-right: 10px;
            width: 16px;
        }
        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .breadcrumb {
            color: #666;
            font-size: 14px;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        /* 搜索区域 */
        .search-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .search-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group label {
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }
        
        .form-group input,
        .form-group select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-success {
            background: #27ae60;
            color: white;
        }
        
        .btn-warning {
            background: #f39c12;
            color: white;
        }
        
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        
        .btn:hover {
            opacity: 0.8;
            transform: translateY(-1px);
        }
        
        /* 工具栏 */
        .toolbar {
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            gap: 10px;
        }
        
        /* 表格样式 */
        .table-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .table-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            font-weight: 600;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #555;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .award-tag {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .award-first {
            background: #ffebee;
            color: #c62828;
        }
        
        .award-second {
            background: #fff3e0;
            color: #f57c00;
        }
        
        .award-third {
            background: #e8f5e8;
            color: #2e7d32;
        }
        
        .award-excellence {
            background: #e3f2fd;
            color: #1976d2;
        }
        
        .level-tag {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .level-national {
            background: #ffebee;
            color: #c62828;
        }
        
        .level-provincial {
            background: #e8f5e8;
            color: #2e7d32;
        }
        
        .level-school {
            background: #e3f2fd;
            color: #1976d2;
        }
        
        .action-buttons {
            display: flex;
            gap: 5px;
        }
        
        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
        }
        
        /* 分页 */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            gap: 10px;
        }
        
        .page-btn {
            padding: 6px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.3s;
        }
        
        .page-btn:hover {
            background: #f5f5f5;
        }
        
        .page-btn.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }
        
        .checkbox {
            width: 16px;
            height: 16px;
        }
        
        .certificate-preview {
            width: 40px;
            height: 30px;
            background: #f0f0f0;
            border: 1px solid #ddd;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #666;
            cursor: pointer;
        }
        
        .certificate-preview:hover {
            background: #e0e0e0;
        }
        
        .students-list {
            max-width: 150px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="logo">
                <h2>数字画像系统</h2>
            </div>
            <div class="menu-item">
                <i>📊</i> 项目管理
            </div>
            <div class="menu-item active">
                <i>🏆</i> 竞赛管理
            </div>
            <div class="menu-item">
                <i>👥</i> 用户管理
            </div>
            <div class="menu-item">
                <i>🏢</i> 部门管理
            </div>
            <div class="menu-item">
                <i>🔐</i> 角色管理
            </div>
            <div class="menu-item">
                <i>📈</i> 数据分析
            </div>
            <div class="menu-item">
                <i>⚙️</i> 系统设置
            </div>
        </div>
        
        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 页面头部 -->
            <div class="header">
                <div class="breadcrumb">
                    首页 > 竞赛管理 > 竞赛列表
                </div>
                <div class="user-info">
                    <span>欢迎，张教授</span>
                    <button class="btn btn-primary btn-small">退出</button>
                </div>
            </div>
            
            <!-- 搜索区域 -->
            <div class="search-section">
                <div class="search-form">
                    <div class="form-group">
                        <label>竞赛名称</label>
                        <input type="text" placeholder="请输入竞赛名称">
                    </div>
                    <div class="form-group">
                        <label>竞赛类型</label>
                        <select>
                            <option value="">全部类型</option>
                            <option value="1">学科竞赛</option>
                            <option value="2">创新创业</option>
                            <option value="3">技能竞赛</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>竞赛级别</label>
                        <select>
                            <option value="">全部级别</option>
                            <option value="1">国家级</option>
                            <option value="2">省级</option>
                            <option value="3">校级</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>获奖等级</label>
                        <select>
                            <option value="">全部等级</option>
                            <option value="1">一等奖</option>
                            <option value="2">二等奖</option>
                            <option value="3">三等奖</option>
                            <option value="4">优秀奖</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>获奖时间</label>
                        <input type="date">
                    </div>
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <div style="display: flex; gap: 10px;">
                            <button class="btn btn-primary">查询</button>
                            <button class="btn">重置</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 工具栏 -->
            <div class="toolbar">
                <button class="btn btn-success">新增竞赛</button>
                <button class="btn btn-warning">批量导入</button>
                <button class="btn btn-primary">导出数据</button>
                <button class="btn btn-danger">批量删除</button>
            </div>
            
            <!-- 数据表格 -->
            <div class="table-container">
                <div class="table-header">
                    竞赛列表 (共 18 条记录)
                </div>
                <table>
                    <thead>
                        <tr>
                            <th><input type="checkbox" class="checkbox"></th>
                            <th>竞赛编号</th>
                            <th>竞赛名称</th>
                            <th>竞赛类型</th>
                            <th>竞赛级别</th>
                            <th>获奖等级</th>
                            <th>指导教师</th>
                            <th>参赛学生</th>
                            <th>获奖时间</th>
                            <th>获奖证书</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><input type="checkbox" class="checkbox"></td>
                            <td>C001</td>
                            <td>全国大学生数学建模竞赛</td>
                            <td>学科竞赛</td>
                            <td><span class="level-tag level-national">国家级</span></td>
                            <td><span class="award-tag award-first">一等奖</span></td>
                            <td>张教授(60%), 李教授(40%)</td>
                            <td class="students-list">王同学, 李同学, 陈同学</td>
                            <td>2023-11-15</td>
                            <td>
                                <div class="certificate-preview" title="点击预览证书">
                                    📄
                                </div>
                            </td>
                            <td>2025-01-19 10:30:25</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn btn-primary btn-small">编辑</button>
                                    <button class="btn btn-warning btn-small">详情</button>
                                    <button class="btn btn-danger btn-small">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="checkbox"></td>
                            <td>C002</td>
                            <td>ACM国际大学生程序设计竞赛</td>
                            <td>学科竞赛</td>
                            <td><span class="level-tag level-national">国家级</span></td>
                            <td><span class="award-tag award-second">二等奖</span></td>
                            <td>王教授(100%)</td>
                            <td class="students-list">赵同学, 钱同学, 孙同学</td>
                            <td>2023-10-20</td>
                            <td>
                                <div class="certificate-preview" title="点击预览证书">
                                    📄
                                </div>
                            </td>
                            <td>2025-01-18 14:22:18</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn btn-primary btn-small">编辑</button>
                                    <button class="btn btn-warning btn-small">详情</button>
                                    <button class="btn btn-danger btn-small">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="checkbox"></td>
                            <td>C003</td>
                            <td>中国"互联网+"大学生创新创业大赛</td>
                            <td>创新创业</td>
                            <td><span class="level-tag level-provincial">省级</span></td>
                            <td><span class="award-tag award-third">三等奖</span></td>
                            <td>李教授(70%), 张教授(30%)</td>
                            <td class="students-list">周同学, 吴同学, 郑同学, 王同学</td>
                            <td>2023-09-12</td>
                            <td>
                                <div class="certificate-preview" title="点击预览证书">
                                    📄
                                </div>
                            </td>
                            <td>2025-01-17 09:15:42</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn btn-primary btn-small">编辑</button>
                                    <button class="btn btn-warning btn-small">详情</button>
                                    <button class="btn btn-danger btn-small">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="checkbox"></td>
                            <td>C004</td>
                            <td>全国大学生电子设计竞赛</td>
                            <td>技能竞赛</td>
                            <td><span class="level-tag level-national">国家级</span></td>
                            <td><span class="award-tag award-excellence">优秀奖</span></td>
                            <td>陈教授(100%)</td>
                            <td class="students-list">冯同学, 陈同学</td>
                            <td>2023-08-25</td>
                            <td>
                                <div class="certificate-preview" title="点击预览证书">
                                    📄
                                </div>
                            </td>
                            <td>2025-01-16 16:45:33</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn btn-primary btn-small">编辑</button>
                                    <button class="btn btn-warning btn-small">详情</button>
                                    <button class="btn btn-danger btn-small">删除</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
                
                <!-- 分页 -->
                <div class="pagination">
                    <button class="page-btn">上一页</button>
                    <button class="page-btn active">1</button>
                    <button class="page-btn">2</button>
                    <button class="page-btn">3</button>
                    <button class="page-btn">下一页</button>
                    <span style="margin-left: 20px;">共 18 条记录，每页 10 条</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
