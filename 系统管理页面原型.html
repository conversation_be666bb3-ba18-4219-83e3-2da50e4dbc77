<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统管理 - 数字画像系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #409eff;
            margin-bottom: 10px;
        }
        
        .breadcrumb {
            color: #666;
            font-size: 14px;
        }
        
        .tabs {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .tab-header {
            display: flex;
            border-bottom: 1px solid #eee;
        }
        
        .tab-item {
            padding: 15px 25px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }
        
        .tab-item.active {
            color: #409eff;
            border-bottom-color: #409eff;
            background: #f0f9ff;
        }
        
        .tab-item:hover {
            background: #f5f7fa;
        }
        
        .tab-content {
            padding: 20px;
        }
        
        .tab-pane {
            display: none;
        }
        
        .tab-pane.active {
            display: block;
        }
        
        .search-form {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
            align-items: center;
        }
        
        .form-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .form-item label {
            min-width: 80px;
            font-weight: 500;
        }
        
        .form-item input, .form-item select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 180px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-primary { background: #409eff; color: white; }
        .btn-default { background: #f4f4f5; color: #606266; }
        .btn-success { background: #67c23a; color: white; }
        .btn-danger { background: #f56c6c; color: white; }
        .btn-warning { background: #e6a23c; color: white; }
        .btn-info { background: #909399; color: white; }
        
        .btn:hover { opacity: 0.8; }
        
        .toolbar {
            margin-bottom: 20px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 6px;
            overflow: hidden;
        }
        
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        tr:hover {
            background: #f5f7fa;
        }
        
        .status-switch {
            width: 40px;
            height: 20px;
            background: #ddd;
            border-radius: 10px;
            position: relative;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .status-switch.active {
            background: #67c23a;
        }
        
        .status-switch::after {
            content: '';
            width: 16px;
            height: 16px;
            background: white;
            border-radius: 50%;
            position: absolute;
            top: 2px;
            left: 2px;
            transition: left 0.3s;
        }
        
        .status-switch.active::after {
            left: 22px;
        }
        
        .dept-tree {
            background: white;
            border-radius: 6px;
            padding: 20px;
        }
        
        .tree-node {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .tree-node:last-child {
            border-bottom: none;
        }
        
        .node-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            border-radius: 4px;
        }
        
        .node-content:hover {
            background: #f5f7fa;
        }
        
        .node-label {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .node-icon {
            width: 16px;
            height: 16px;
            background: #409eff;
            border-radius: 2px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 10px;
        }
        
        .node-actions {
            display: flex;
            gap: 5px;
        }
        
        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
        }
        
        .child-nodes {
            margin-left: 30px;
            margin-top: 5px;
        }
        
        .role-tag {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .role-enabled {
            background: #f0f9ff;
            color: #67c23a;
            border: 1px solid #b3e19d;
        }
        
        .role-disabled {
            background: #fef0f0;
            color: #f56c6c;
            border: 1px solid #fbc4c4;
        }
        
        .action-buttons {
            display: flex;
            gap: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <h1>系统管理</h1>
            <div class="breadcrumb">系统管理 > 系统设置</div>
        </div>
        
        <!-- 选项卡 -->
        <div class="tabs">
            <div class="tab-header">
                <div class="tab-item active" onclick="switchTab('user')">用户管理</div>
                <div class="tab-item" onclick="switchTab('dept')">部门管理</div>
                <div class="tab-item" onclick="switchTab('role')">角色管理</div>
            </div>
            
            <div class="tab-content">
                <!-- 用户管理 -->
                <div id="user-tab" class="tab-pane active">
                    <div class="search-form">
                        <div class="form-row">
                            <div class="form-item">
                                <label>用户名:</label>
                                <input type="text" placeholder="请输入用户名">
                            </div>
                            <div class="form-item">
                                <label>部门:</label>
                                <select>
                                    <option value="">请选择部门</option>
                                    <option value="1">计算机学院</option>
                                    <option value="2">软件学院</option>
                                </select>
                            </div>
                            <div class="form-item">
                                <label>状态:</label>
                                <select>
                                    <option value="">全部</option>
                                    <option value="1">启用</option>
                                    <option value="0">禁用</option>
                                </select>
                            </div>
                            <div class="form-item">
                                <button class="btn btn-primary">查询</button>
                                <button class="btn btn-default">重置</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="toolbar">
                        <button class="btn btn-success">新增用户</button>
                        <button class="btn btn-info">批量导入</button>
                        <button class="btn btn-warning">导出数据</button>
                    </div>
                    
                    <table>
                        <thead>
                            <tr>
                                <th>用户名</th>
                                <th>昵称</th>
                                <th>邮箱</th>
                                <th>部门</th>
                                <th>角色</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>admin</td>
                                <td>系统管理员</td>
                                <td><EMAIL></td>
                                <td>系统管理部</td>
                                <td>超级管理员</td>
                                <td><div class="status-switch active"></div></td>
                                <td>2023-01-01 00:00:00</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-primary btn-small">编辑</button>
                                        <button class="btn btn-warning btn-small">重置密码</button>
                                        <button class="btn btn-danger btn-small">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>teacher01</td>
                                <td>张教授</td>
                                <td><EMAIL></td>
                                <td>计算机学院</td>
                                <td>教师</td>
                                <td><div class="status-switch active"></div></td>
                                <td>2023-03-15 14:30:25</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-primary btn-small">编辑</button>
                                        <button class="btn btn-warning btn-small">重置密码</button>
                                        <button class="btn btn-danger btn-small">删除</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 部门管理 -->
                <div id="dept-tab" class="tab-pane">
                    <div class="toolbar">
                        <button class="btn btn-success">新增部门</button>
                        <button class="btn btn-info">展开全部</button>
                        <button class="btn btn-default">收起全部</button>
                    </div>
                    
                    <div class="dept-tree">
                        <div class="tree-node">
                            <div class="node-content">
                                <div class="node-label">
                                    <div class="node-icon">学</div>
                                    <span>某某大学</span>
                                </div>
                                <div class="node-actions">
                                    <button class="btn btn-primary btn-small">编辑</button>
                                    <button class="btn btn-success btn-small">新增下级</button>
                                </div>
                            </div>
                            <div class="child-nodes">
                                <div class="tree-node">
                                    <div class="node-content">
                                        <div class="node-label">
                                            <div class="node-icon">院</div>
                                            <span>计算机学院</span>
                                        </div>
                                        <div class="node-actions">
                                            <button class="btn btn-primary btn-small">编辑</button>
                                            <button class="btn btn-success btn-small">新增下级</button>
                                            <button class="btn btn-danger btn-small">删除</button>
                                        </div>
                                    </div>
                                    <div class="child-nodes">
                                        <div class="tree-node">
                                            <div class="node-content">
                                                <div class="node-label">
                                                    <div class="node-icon">系</div>
                                                    <span>计算机科学与技术系</span>
                                                </div>
                                                <div class="node-actions">
                                                    <button class="btn btn-primary btn-small">编辑</button>
                                                    <button class="btn btn-success btn-small">新增下级</button>
                                                    <button class="btn btn-danger btn-small">删除</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="tree-node">
                                    <div class="node-content">
                                        <div class="node-label">
                                            <div class="node-icon">院</div>
                                            <span>软件学院</span>
                                        </div>
                                        <div class="node-actions">
                                            <button class="btn btn-primary btn-small">编辑</button>
                                            <button class="btn btn-success btn-small">新增下级</button>
                                            <button class="btn btn-danger btn-small">删除</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 角色管理 -->
                <div id="role-tab" class="tab-pane">
                    <div class="toolbar">
                        <button class="btn btn-success">新增角色</button>
                    </div>
                    
                    <table>
                        <thead>
                            <tr>
                                <th>角色名称</th>
                                <th>角色标识</th>
                                <th>排序</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>超级管理员</td>
                                <td>admin</td>
                                <td>1</td>
                                <td><span class="role-tag role-enabled">启用</span></td>
                                <td>2023-01-01 00:00:00</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-primary btn-small">编辑</button>
                                        <button class="btn btn-info btn-small">权限配置</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>教师</td>
                                <td>teacher</td>
                                <td>2</td>
                                <td><span class="role-tag role-enabled">启用</span></td>
                                <td>2023-01-01 00:00:00</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-primary btn-small">编辑</button>
                                        <button class="btn btn-info btn-small">权限配置</button>
                                        <button class="btn btn-danger btn-small">删除</button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>学生</td>
                                <td>student</td>
                                <td>3</td>
                                <td><span class="role-tag role-disabled">禁用</span></td>
                                <td>2023-01-01 00:00:00</td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn btn-primary btn-small">编辑</button>
                                        <button class="btn btn-info btn-small">权限配置</button>
                                        <button class="btn btn-danger btn-small">删除</button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function switchTab(tabName) {
            // 隐藏所有选项卡内容
            const tabPanes = document.querySelectorAll('.tab-pane');
            tabPanes.forEach(pane => pane.classList.remove('active'));
            
            // 移除所有选项卡的激活状态
            const tabItems = document.querySelectorAll('.tab-item');
            tabItems.forEach(item => item.classList.remove('active'));
            
            // 显示选中的选项卡内容
            document.getElementById(tabName + '-tab').classList.add('active');
            
            // 激活选中的选项卡
            event.target.classList.add('active');
        }
    </script>
</body>
</html>
