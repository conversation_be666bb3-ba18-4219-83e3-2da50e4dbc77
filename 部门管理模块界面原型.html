<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>部门管理模块 - 数字画像系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }
        
        .container {
            display: flex;
            min-height: 100vh;
        }
        
        /* 侧边栏样式 */
        .sidebar {
            width: 240px;
            background: #2c3e50;
            color: white;
            padding: 20px 0;
        }
        
        .logo {
            text-align: center;
            padding: 20px;
            border-bottom: 1px solid #34495e;
            margin-bottom: 20px;
        }
        
        .logo h2 {
            color: #3498db;
        }
        
        .menu-item {
            padding: 12px 20px;
            cursor: pointer;
            transition: background 0.3s;
            border-left: 3px solid transparent;
        }
        
        .menu-item:hover {
            background: #34495e;
        }
        
        .menu-item.active {
            background: #3498db;
            border-left-color: #2980b9;
        }
        
        .menu-item i {
            margin-right: 10px;
            width: 16px;
        }
        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            padding: 20px;
            display: flex;
            gap: 20px;
        }
        
        .left-panel {
            width: 300px;
        }
        
        .right-panel {
            flex: 1;
        }
        
        .header {
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
            grid-column: 1 / -1;
        }
        
        .breadcrumb {
            color: #666;
            font-size: 14px;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        /* 部门树样式 */
        .dept-tree-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .tree-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            font-weight: 600;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .tree-content {
            padding: 15px;
            max-height: 600px;
            overflow-y: auto;
        }
        
        .tree-node {
            margin-bottom: 5px;
        }
        
        .tree-item {
            padding: 8px 12px;
            cursor: pointer;
            border-radius: 4px;
            transition: background 0.3s;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .tree-item:hover {
            background: #f5f5f5;
        }
        
        .tree-item.active {
            background: #e3f2fd;
            color: #1976d2;
        }
        
        .tree-icon {
            width: 16px;
            text-align: center;
        }
        
        .tree-children {
            margin-left: 20px;
            border-left: 1px dashed #ddd;
            padding-left: 10px;
        }
        
        /* 搜索区域 */
        .search-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .search-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group label {
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }
        
        .form-group input,
        .form-group select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-success {
            background: #27ae60;
            color: white;
        }
        
        .btn-warning {
            background: #f39c12;
            color: white;
        }
        
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        
        .btn:hover {
            opacity: 0.8;
            transform: translateY(-1px);
        }
        
        /* 工具栏 */
        .toolbar {
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            gap: 10px;
        }
        
        /* 表格样式 */
        .table-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .table-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            font-weight: 600;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #555;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .status-tag {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-active {
            background: #e8f5e8;
            color: #2e7d32;
        }
        
        .status-inactive {
            background: #ffebee;
            color: #c62828;
        }
        
        .action-buttons {
            display: flex;
            gap: 5px;
        }
        
        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
        }
        
        /* 分页 */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            gap: 10px;
        }
        
        .page-btn {
            padding: 6px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.3s;
        }
        
        .page-btn:hover {
            background: #f5f5f5;
        }
        
        .page-btn.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }
        
        .checkbox {
            width: 16px;
            height: 16px;
        }
        
        .dept-level {
            padding: 2px 6px;
            background: #f0f0f0;
            border-radius: 4px;
            font-size: 12px;
            color: #666;
        }
        
        .main-content-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="logo">
                <h2>数字画像系统</h2>
            </div>
            <div class="menu-item">
                <i>📊</i> 项目管理
            </div>
            <div class="menu-item">
                <i>🏆</i> 竞赛管理
            </div>
            <div class="menu-item">
                <i>👥</i> 用户管理
            </div>
            <div class="menu-item active">
                <i>🏢</i> 部门管理
            </div>
            <div class="menu-item">
                <i>🔐</i> 角色管理
            </div>
            <div class="menu-item">
                <i>📈</i> 数据分析
            </div>
            <div class="menu-item">
                <i>⚙️</i> 系统设置
            </div>
        </div>
        
        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 左侧部门树 -->
            <div class="left-panel">
                <div class="dept-tree-container">
                    <div class="tree-header">
                        <span>部门结构</span>
                        <button class="btn btn-success btn-small">新增部门</button>
                    </div>
                    <div class="tree-content">
                        <div class="tree-node">
                            <div class="tree-item active">
                                <span class="tree-icon">🏛️</span>
                                <span>华南理工大学</span>
                            </div>
                            <div class="tree-children">
                                <div class="tree-node">
                                    <div class="tree-item">
                                        <span class="tree-icon">🏫</span>
                                        <span>计算机学院</span>
                                    </div>
                                    <div class="tree-children">
                                        <div class="tree-node">
                                            <div class="tree-item">
                                                <span class="tree-icon">📚</span>
                                                <span>软件工程系</span>
                                            </div>
                                        </div>
                                        <div class="tree-node">
                                            <div class="tree-item">
                                                <span class="tree-icon">📚</span>
                                                <span>计算机科学系</span>
                                            </div>
                                        </div>
                                        <div class="tree-node">
                                            <div class="tree-item">
                                                <span class="tree-icon">📚</span>
                                                <span>信息工程系</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="tree-node">
                                    <div class="tree-item">
                                        <span class="tree-icon">🏫</span>
                                        <span>电子信息学院</span>
                                    </div>
                                    <div class="tree-children">
                                        <div class="tree-node">
                                            <div class="tree-item">
                                                <span class="tree-icon">📚</span>
                                                <span>电子工程系</span>
                                            </div>
                                        </div>
                                        <div class="tree-node">
                                            <div class="tree-item">
                                                <span class="tree-icon">📚</span>
                                                <span>通信工程系</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="tree-node">
                                    <div class="tree-item">
                                        <span class="tree-icon">🏫</span>
                                        <span>机械工程学院</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 右侧内容区域 -->
            <div class="right-panel">
                <div class="main-content-grid">
                    <!-- 页面头部 -->
                    <div class="header">
                        <div class="breadcrumb">
                            首页 > 系统管理 > 部门管理
                        </div>
                        <div class="user-info">
                            <span>欢迎，管理员</span>
                            <button class="btn btn-primary btn-small">退出</button>
                        </div>
                    </div>
                    
                    <!-- 搜索区域 -->
                    <div class="search-section">
                        <div class="search-form">
                            <div class="form-group">
                                <label>部门名称</label>
                                <input type="text" placeholder="请输入部门名称">
                            </div>
                            <div class="form-group">
                                <label>部门状态</label>
                                <select>
                                    <option value="">全部状态</option>
                                    <option value="1">启用</option>
                                    <option value="0">禁用</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div style="display: flex; gap: 10px;">
                                    <button class="btn btn-primary">查询</button>
                                    <button class="btn">重置</button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 工具栏 -->
                    <div class="toolbar">
                        <button class="btn btn-success">新增部门</button>
                        <button class="btn btn-primary">展开全部</button>
                        <button class="btn">折叠全部</button>
                        <button class="btn btn-danger">批量删除</button>
                    </div>
                    
                    <!-- 数据表格 -->
                    <div class="table-container">
                        <div class="table-header">
                            部门列表 (共 15 条记录)
                        </div>
                        <table>
                            <thead>
                                <tr>
                                    <th><input type="checkbox" class="checkbox"></th>
                                    <th>部门名称</th>
                                    <th>部门层级</th>
                                    <th>负责人</th>
                                    <th>联系电话</th>
                                    <th>排序</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td><input type="checkbox" class="checkbox"></td>
                                    <td>华南理工大学</td>
                                    <td><span class="dept-level">一级</span></td>
                                    <td>校长</td>
                                    <td>020-87110000</td>
                                    <td>1</td>
                                    <td><span class="status-tag status-active">启用</span></td>
                                    <td>2023-08-01 09:00:00</td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn btn-primary btn-small">编辑</button>
                                            <button class="btn btn-success btn-small">新增下级</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><input type="checkbox" class="checkbox"></td>
                                    <td>├─ 计算机学院</td>
                                    <td><span class="dept-level">二级</span></td>
                                    <td>张院长</td>
                                    <td>020-87110001</td>
                                    <td>1</td>
                                    <td><span class="status-tag status-active">启用</span></td>
                                    <td>2023-08-01 09:00:00</td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn btn-primary btn-small">编辑</button>
                                            <button class="btn btn-success btn-small">新增下级</button>
                                            <button class="btn btn-danger btn-small">删除</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><input type="checkbox" class="checkbox"></td>
                                    <td>│　├─ 软件工程系</td>
                                    <td><span class="dept-level">三级</span></td>
                                    <td>李主任</td>
                                    <td>020-87110002</td>
                                    <td>1</td>
                                    <td><span class="status-tag status-active">启用</span></td>
                                    <td>2023-08-01 09:00:00</td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn btn-primary btn-small">编辑</button>
                                            <button class="btn btn-danger btn-small">删除</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><input type="checkbox" class="checkbox"></td>
                                    <td>│　├─ 计算机科学系</td>
                                    <td><span class="dept-level">三级</span></td>
                                    <td>王主任</td>
                                    <td>020-87110003</td>
                                    <td>2</td>
                                    <td><span class="status-tag status-active">启用</span></td>
                                    <td>2023-08-01 09:00:00</td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn btn-primary btn-small">编辑</button>
                                            <button class="btn btn-danger btn-small">删除</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><input type="checkbox" class="checkbox"></td>
                                    <td>│　└─ 信息工程系</td>
                                    <td><span class="dept-level">三级</span></td>
                                    <td>陈主任</td>
                                    <td>020-87110004</td>
                                    <td>3</td>
                                    <td><span class="status-tag status-active">启用</span></td>
                                    <td>2023-08-01 09:00:00</td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn btn-primary btn-small">编辑</button>
                                            <button class="btn btn-danger btn-small">删除</button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td><input type="checkbox" class="checkbox"></td>
                                    <td>├─ 电子信息学院</td>
                                    <td><span class="dept-level">二级</span></td>
                                    <td>刘院长</td>
                                    <td>020-87110005</td>
                                    <td>2</td>
                                    <td><span class="status-tag status-active">启用</span></td>
                                    <td>2023-08-01 09:00:00</td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn btn-primary btn-small">编辑</button>
                                            <button class="btn btn-success btn-small">新增下级</button>
                                            <button class="btn btn-danger btn-small">删除</button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        
                        <!-- 分页 -->
                        <div class="pagination">
                            <button class="page-btn">上一页</button>
                            <button class="page-btn active">1</button>
                            <button class="page-btn">2</button>
                            <button class="page-btn">下一页</button>
                            <span style="margin-left: 20px;">共 15 条记录，每页 10 条</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
