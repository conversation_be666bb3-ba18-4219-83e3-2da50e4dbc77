# 基于Spring Boot和Vue3的教师数字画像系统设计与实现

## 摘要

随着教育信息化的深入发展，传统的教师评价和管理方式已无法满足现代教育管理的需求。本文设计并实现了一个基于Spring Boot和Vue3技术栈的教师数字画像系统，重点研究了项目管理、竞赛管理、用户管理、部门管理和角色管理五个核心功能模块。系统采用前后端分离的微服务架构，后端基于Spring Boot框架构建RESTful API，前端采用Vue3和Element Plus构建现代化用户界面，数据层使用MyBatis Plus和MySQL实现数据持久化。通过对各模块的详细设计与实现，系统能够有效记录和管理教师的项目成果、竞赛指导情况，并提供完善的用户权限管理功能，为教育机构的教师评价和人才管理提供了有力的技术支撑。实践表明，该系统具有良好的可扩展性、稳定性和用户体验，能够满足教育机构数字化管理的实际需求。

**关键词：** 数字画像；教师管理；Spring Boot；Vue3；项目管理；竞赛管理；权限管理

## Abstract

With the in-depth development of educational informatization, traditional teacher evaluation and management methods can no longer meet the needs of modern educational management. This paper designs and implements a teacher digital portrait system based on Spring Boot and Vue3 technology stack, focusing on five core functional modules: project management, competition management, user management, department management and role management. The system adopts a front-end and back-end separated microservice architecture, with the back-end building RESTful APIs based on the Spring Boot framework, the front-end using Vue3 and Element Plus to build a modern user interface, and the data layer using MyBatis Plus and MySQL for data persistence. Through detailed design and implementation of each module, the system can effectively record and manage teachers' project achievements and competition guidance, and provide comprehensive user permission management functions, providing strong technical support for educational institutions' teacher evaluation and talent management. Practice shows that the system has good scalability, stability and user experience, and can meet the actual needs of digital management of educational institutions.

**Keywords:** Digital Portrait; Teacher Management; Spring Boot; Vue3; Project Management; Competition Management; Permission Management

## 目录

第1章 绪论 ......................................................... 1
1.1 研究背景与意义 ................................................ 1
1.2 国内外研究现状 ................................................ 2
1.3 研究内容与目标 ................................................ 3
1.4 论文组织结构 .................................................. 4

第2章 相关技术介绍 ................................................ 5
2.1 Spring Boot框架 ............................................... 5
2.2 Vue3框架 ..................................................... 6
2.3 MyBatis Plus ................................................. 7
2.4 Element Plus组件库 ........................................... 8

第3章 系统分析 .................................................... 9
3.1 架构概述 ..................................................... 9
3.2 系统开发环境 ................................................. 12
3.3 系统任务的可行性分析 ......................................... 13

第4章 系统设计 .................................................... 15
4.1 设计指导思想和原则 ........................................... 15
4.2 架构概述 ..................................................... 16
4.3 系统的功能结构设计 ........................................... 18
4.4 系统控制流程 ................................................. 20

第5章 系统实现 .................................................... 22
5.1 项目管理模块实现 ............................................. 22
5.2 竞赛管理模块实现 ............................................. 25
5.3 用户管理模块实现 ............................................. 28
5.4 部门管理模块实现 ............................................. 31
5.5 角色管理模块实现 ............................................. 34

第6章 系统测试 .................................................... 37
6.1 测试方案及测试用例 ........................................... 37
6.2 功能测试 ..................................................... 39
6.3 性能测试 ..................................................... 42

第7章 总结与展望 .................................................. 44
7.1 工作总结 ..................................................... 44
7.2 存在的问题 ................................................... 45
7.3 未来展望 ..................................................... 46

参考文献 .......................................................... 47
致谢 .............................................................. 48
附录 .............................................................. 49

## 1. 绪论

### 1.1 研究背景与意义

在数字化转型的时代背景下，教育行业正面临着前所未有的变革。传统的教师评价体系主要依靠纸质档案和人工统计，存在信息分散、数据不准确、查询困难等问题。随着大数据、人工智能等技术的快速发展，构建教师数字画像成为提升教育管理水平的重要手段。

教师数字画像是指通过收集、整理、分析教师在教学、科研、社会服务等方面的数据，形成多维度、立体化的教师能力和成果展示。这不仅有助于教育管理者全面了解教师的综合素质，也为教师个人发展提供了科学的参考依据。

本研究的意义在于：
1. **提升管理效率**：通过信息化手段替代传统的人工管理方式，大幅提升教师信息管理的效率和准确性
2. **促进公平评价**：建立标准化的数据记录和评价体系，确保教师评价的客观性和公正性
3. **支持决策分析**：为教育管理者提供数据支撑，辅助人才引进、职称评定、绩效考核等决策
4. **推动技术创新**：探索现代Web技术在教育管理领域的应用，为相关系统开发提供技术参考

### 1.2 国内外研究现状

#### 1.2.1 国外研究现状

国外在教师管理信息化方面起步较早，已有多个成熟的教师管理系统投入使用。美国的教师评价系统注重多元化评价指标，包括学生成绩、课堂观察、专业发展等多个维度。欧洲国家则更加注重教师的终身学习和职业发展记录。

在技术实现方面，国外系统普遍采用云计算、大数据分析等先进技术，具有较强的数据处理能力和用户体验。

#### 1.2.2 国内研究现状

国内教师管理信息化起步相对较晚，但发展迅速。目前主要有以下几类系统：
1. **高校人事管理系统**：主要用于教师基本信息管理和人事流程处理
2. **科研管理系统**：专注于科研项目和成果的管理
3. **教学管理系统**：侧重于教学活动和课程管理

然而，现有系统大多功能单一，缺乏统一的数字画像构建能力，难以形成教师的全面评价体系。

### 1.3 研究内容与目标

本研究的主要内容包括：
1. **需求分析**：深入调研教育机构对教师管理的实际需求，确定系统功能范围
2. **系统设计**：设计系统的整体架构、数据模型和功能模块
3. **核心模块实现**：重点实现项目管理、竞赛管理、用户管理、部门管理和角色管理五个核心模块
4. **系统测试**：对系统进行功能测试和性能测试，验证系统的可靠性和稳定性

研究目标：
1. 构建一个功能完善、技术先进的教师数字画像系统
2. 实现教师项目成果和竞赛指导情况的数字化管理
3. 建立完善的用户权限管理体系
4. 为教育机构提供高效、便捷的教师管理工具

### 1.4 论文组织结构

本论文共分为六章：
- 第1章为绪论，介绍研究背景、意义、现状和目标
- 第2章为需求分析，详细分析系统的功能需求和非功能需求
- 第3章为系统设计，阐述系统架构、数据库设计和接口设计
- 第4章为核心模块实现，详细介绍五个核心模块的具体实现
- 第5章为系统测试，展示测试方案和测试结果
- 第6章为总结与展望，总结研究成果并提出未来发展方向

## 第2章 相关技术介绍

### 2.1 Spring Boot框架

Spring Boot是由Pivotal团队提供的全新框架，其设计目的是用来简化新Spring应用的初始搭建以及开发过程。该框架使用了特定的方式来进行配置，从而使开发人员不再需要定义样板化的配置。

Spring Boot的主要特点包括：

1. **自动配置**：Spring Boot能根据当前类路径下的类、jar包来自动配置Bean，大大减少了配置工作量
2. **起步依赖**：起步依赖本质上是一个Maven项目对象模型，定义了对其他库的传递依赖，简化了依赖管理
3. **内嵌服务器**：Spring Boot应用可以打包成一个可执行的jar包，内嵌Tomcat、Jetty等服务器，便于部署
4. **生产就绪**：提供了健康检查、指标收集、外部配置等生产环境所需的功能

Spring Boot在本系统中的应用主要体现在：
- 快速搭建微服务架构
- 简化配置管理
- 提供统一的异常处理机制
- 集成Spring Security实现安全控制

### 2.2 Vue3框架

Vue3是一套用于构建用户界面的渐进式JavaScript框架。与其他大型框架不同的是，Vue被设计为可以自底向上逐层应用。Vue3相比Vue2有了重大改进：

1. **Composition API**：提供了更好的逻辑复用和代码组织方式，解决了Vue2中mixins的问题
2. **响应式系统重写**：基于Proxy的响应式系统，性能更优，支持更多数据类型
3. **Tree-shaking支持**：更好的Tree-shaking支持，打包体积更小
4. **TypeScript支持**：从源码层面用TypeScript重写，提供更好的类型推导

Vue3在本系统前端开发中的优势：
- 组件化开发提高代码复用性
- 响应式数据绑定简化状态管理
- 虚拟DOM提升渲染性能
- 丰富的生态系统支持

### 2.3 MyBatis Plus

MyBatis Plus是一个MyBatis的增强工具，在MyBatis的基础上只做增强不做改变，为简化开发、提高效率而生。

主要特性包括：

1. **无侵入**：只做增强不做改变，引入它不会对现有工程产生影响
2. **损耗小**：启动即会自动注入基本CRUD，性能基本无损耗
3. **强大的CRUD操作**：内置通用Mapper、通用Service，仅仅通过少量配置即可实现单表大部分CRUD操作
4. **代码生成器**：采用代码或者Maven插件可快速生成Mapper、Model、Service、Controller层代码
5. **条件构造器**：强大的条件构造器，满足各类使用需求
6. **分页插件**：支持多种数据库的分页操作

在本系统中MyBatis Plus的应用：
- 简化数据访问层开发
- 提供统一的CRUD操作接口
- 支持复杂查询条件构建
- 实现数据分页功能

### 2.4 Element Plus组件库

Element Plus是基于Vue3的桌面端组件库，是Element UI的Vue3版本。它提供了丰富的UI组件，帮助开发者快速构建功能丰富、体验良好的Web应用。

主要特点：

1. **组件丰富**：提供60+高质量组件，覆盖大部分业务场景
2. **设计统一**：遵循统一的设计规范，保证界面一致性
3. **TypeScript支持**：完整的TypeScript类型定义
4. **主题定制**：支持主题定制，满足不同品牌需求
5. **国际化**：内置国际化支持

在本系统中的应用：
- 快速构建管理后台界面
- 提供统一的交互体验
- 减少UI开发工作量
- 保证界面的专业性和美观性

## 第3章 系统分析

### 3.1 系统分析概述

本系统采用分层架构设计思想，将整个系统划分为表示层、业务逻辑层、数据访问层和数据存储层。在功能设计上，系统以教师数字画像构建为核心目标，围绕项目管理和竞赛管理两个主要业务场景，配套完善的系统管理功能，形成了完整的功能体系。

系统架构设计遵循高内聚、低耦合的原则，各功能模块相对独立，通过标准化的接口进行交互。采用前后端分离的开发模式，前端负责用户交互和数据展示，后端负责业务逻辑处理和数据管理，通过RESTful API进行数据交换。

#### 3.1.1 系统功能结构分析

**1. 功能结构图**

系统功能结构按照业务领域进行模块划分，描述系统的模块以及各模块的功能，如图3.1所示。

```plantuml
@startuml
!define RECTANGLE class

package "教师数字画像系统" {
    package "核心业务模块" {
        RECTANGLE "项目管理模块" as ProjectModule {
            + 项目信息管理
            + 项目成员管理
            + 项目状态跟踪
            + 佐证材料管理
        }

        RECTANGLE "竞赛管理模块" as CompetitionModule {
            + 竞赛信息管理
            + 获奖信息管理
            + 指导教师管理
            + 竞赛分类管理
        }
    }

    package "系统管理模块" {
        RECTANGLE "用户管理" as UserMgmt {
            + 用户信息管理
            + 用户状态管理
            + 登录记录管理
            + 密码安全管理
        }

        RECTANGLE "部门管理" as DeptMgmt {
            + 部门层级管理
            + 部门信息管理
            + 部门关系维护
            + 负责人管理
        }

        RECTANGLE "角色管理" as RoleMgmt {
            + 角色权限管理
            + 权限分配管理
            + 数据权限控制
            + 角色状态管理
        }
    }

    package "基础支撑模块" {
        RECTANGLE "权限控制模块" as PermissionModule {
            + 菜单权限控制
            + 数据权限控制
            + 接口权限控制
            + 操作权限控制
        }

        RECTANGLE "文件管理模块" as FileModule {
            + 文件上传下载
            + 文件预览
            + 文件存储管理
            + 佐证材料管理
        }

        RECTANGLE "系统监控模块" as MonitorModule {
            + 系统日志管理
            + 操作日志记录
            + 性能监控
            + 异常告警
        }
    }
}

ProjectModule --> PermissionModule
CompetitionModule --> PermissionModule
UserMgmt --> RoleMgmt
UserMgmt --> DeptMgmt
RoleMgmt --> PermissionModule
DeptMgmt --> PermissionModule
ProjectModule --> FileModule
CompetitionModule --> FileModule
ProjectModule --> MonitorModule
CompetitionModule --> MonitorModule
UserMgmt --> MonitorModule

@enduml
```

图3.1 系统功能结构图

**2. 系统架构图**

系统采用前后端分离的微服务架构设计，描述系统的构成要素以及它们之间的逻辑关系，如图3.2所示。

```plantuml
@startuml
!define RECTANGLE class

package "前端层" {
    RECTANGLE "Vue3应用" as Frontend {
        + 用户界面
        + 路由管理
        + 状态管理
        + HTTP请求
    }
}

package "网关层" {
    RECTANGLE "API网关" as Gateway {
        + 请求路由
        + 负载均衡
        + 限流控制
        + 统一认证
    }
}

package "业务服务层" {
    RECTANGLE "用户服务" as UserService {
        + 用户管理
        + 认证授权
        + 权限控制
    }

    RECTANGLE "项目服务" as ProjectService {
        + 项目管理
        + 成员管理
        + 状态跟踪
    }

    RECTANGLE "竞赛服务" as CompetitionService {
        + 竞赛管理
        + 获奖管理
        + 指导管理
    }

    RECTANGLE "系统服务" as SystemService {
        + 部门管理
        + 角色管理
        + 权限管理
    }
}

package "数据访问层" {
    RECTANGLE "数据访问层" as DataAccess {
        + MyBatis Plus
        + 数据源管理
        + 事务管理
        + 连接池
    }
}

package "数据存储层" {
    RECTANGLE "MySQL数据库" as MySQL {
        + 业务数据存储
        + 事务支持
        + 数据备份
    }

    RECTANGLE "Redis缓存" as Redis {
        + 会话存储
        + 数据缓存
        + 分布式锁
    }

    RECTANGLE "文件存储" as FileStorage {
        + 文件上传
        + 文件下载
        + 文件管理
    }
}

Frontend --> Gateway
Gateway --> UserService
Gateway --> ProjectService
Gateway --> CompetitionService
Gateway --> SystemService
UserService --> DataAccess
ProjectService --> DataAccess
CompetitionService --> DataAccess
SystemService --> DataAccess
DataAccess --> MySQL
UserService --> Redis
ProjectService --> FileStorage
CompetitionService --> FileStorage

@enduml
```

图3.2 系统架构图

**3. 设计类图**

系统核心业务类的设计，描述系统内各个对象的相关类以及类间的关系，如图3.3所示。

```plantuml
@startuml

package "项目管理模块" {
    class ProjectsDO {
        -Integer id
        -String name
        -Integer type
        -Integer organizer
        -String organizerOther
        -Integer level
        -String author
        -String member
        -String rate
        -LocalDateTime projectStartTime
        -LocalDateTime projectEndTime
        -Integer status
        -List<String> evidenceUrl
        -String remark
        +getId(): Integer
        +setName(String): void
        +getProjectStartTime(): LocalDateTime
    }

    class ProjectsService {
        +createProjects(ProjectsSaveReqVO): Integer
        +updateProjects(ProjectsSaveReqVO): void
        +deleteProjects(Integer): void
        +getProjects(Integer): ProjectsDO
        +getProjectsPage(ProjectsPageReqVO): PageResult<ProjectsDO>
    }

    class ProjectsController {
        +createProjects(ProjectsSaveReqVO): CommonResult<Integer>
        +updateProjects(ProjectsSaveReqVO): CommonResult<Boolean>
        +deleteProjects(Integer): CommonResult<Boolean>
        +getProjects(Integer): CommonResult<ProjectsRespVO>
        +getProjectsPage(ProjectsPageReqVO): CommonResult<PageResult<ProjectsRespVO>>
    }
}

package "竞赛管理模块" {
    class CompetitionDO {
        -Integer id
        -String name
        -Integer type
        -Integer organizer
        -String organizerOther
        -Integer level
        -String author
        -String member
        -String rate
        -LocalDateTime competitionTime
        -Integer rank
        -List<String> evidenceUrl
        -String remark
        +getId(): Integer
        +setName(String): void
        +getCompetitionTime(): LocalDateTime
    }

    class CompetitionService {
        +createCompetition(CompetitionSaveReqVO): Integer
        +updateCompetition(CompetitionSaveReqVO): void
        +deleteCompetition(Integer): void
        +getCompetition(Integer): CompetitionDO
        +getCompetitionPage(CompetitionPageReqVO): PageResult<CompetitionDO>
    }

    class CompetitionController {
        +createCompetition(CompetitionSaveReqVO): CommonResult<Integer>
        +updateCompetition(CompetitionSaveReqVO): CommonResult<Boolean>
        +deleteCompetition(Integer): CommonResult<Boolean>
        +getCompetition(Integer): CommonResult<CompetitionRespVO>
        +getCompetitionPage(CompetitionPageReqVO): CommonResult<PageResult<CompetitionRespVO>>
    }
}

package "系统管理模块" {
    class AdminUserDO {
        -Long id
        -String username
        -String password
        -String nickname
        -String remark
        -Long deptId
        -Set<Long> postIds
        -String email
        -String mobile
        -Integer sex
        -String avatar
        -Integer status
        +getId(): Long
        +setUsername(String): void
        +getDeptId(): Long
    }

    class DeptDO {
        -Long id
        -String name
        -Long parentId
        -Integer sort
        -Long leaderUserId
        -String phone
        -String email
        -Integer status
        +getId(): Long
        +setName(String): void
        +getParentId(): Long
    }

    class RoleDO {
        -Long id
        -String name
        -String code
        -Integer sort
        -Integer dataScope
        -Set<Long> dataScopeDeptIds
        -Integer status
        -Integer type
        -String remark
        +getId(): Long
        +setName(String): void
        +getDataScope(): Integer
    }
}

package "基础类" {
    class BaseDO {
        -String creator
        -LocalDateTime createTime
        -String updater
        -LocalDateTime updateTime
        -Boolean deleted
        -Long tenantId
        +getCreateTime(): LocalDateTime
        +setCreator(String): void
    }
}

ProjectsController --> ProjectsService
ProjectsService --> ProjectsDO
CompetitionController --> CompetitionService
CompetitionService --> CompetitionDO
ProjectsDO --|> BaseDO
CompetitionDO --|> BaseDO
AdminUserDO --|> BaseDO
DeptDO --|> BaseDO
RoleDO --|> BaseDO
AdminUserDO --> DeptDO : belongs to
AdminUserDO --> RoleDO : has many

@enduml
```

图3.3 系统设计类图

#### 3.1.2 模块需求分析

**1. 项目管理模块需求分析**

（1）模块概述
对教师参与的各类科研项目和教学项目进行全生命周期管理，包括项目立项、进行中、结项等状态跟踪。

（2）活动图
项目管理业务流程如图3.4所示，将业务流程展示为一步步的控制流和数据流。

```plantuml
@startuml
start
:用户登录系统;
:进入项目管理页面;
if (选择操作类型?) then (新增项目)
  :点击新增按钮;
  :填写项目基本信息;
  :添加项目成员;
  :设置成员贡献率;
  :上传佐证材料;
  :保存项目信息;
  :系统验证数据;
  if (数据验证通过?) then (是)
    :保存到数据库;
    :显示成功提示;
  else (否)
    :显示错误信息;
    :返回编辑页面;
  endif
elseif (查询项目) then
  :输入查询条件;
  :执行查询操作;
  :显示项目列表;
elseif (编辑项目) then
  :选择要编辑的项目;
  :加载项目详细信息;
  :修改项目信息;
  :保存修改;
else (删除项目)
  :选择要删除的项目;
  :确认删除操作;
  :执行删除;
  :更新项目列表;
endif
stop
@enduml
```

图3.4 项目管理活动图

（3）用例图
项目管理模块的用例图如图3.5所示，描述参与者角度所观察到的系统功能。

```plantuml
@startuml
left to right direction
actor "教师用户" as Teacher
actor "管理员" as Admin

rectangle "项目管理模块" {
  usecase "项目信息录入" as UC1
  usecase "项目信息查询" as UC2
  usecase "项目信息编辑" as UC3
  usecase "项目信息删除" as UC4
  usecase "项目成员管理" as UC5
  usecase "佐证材料上传" as UC6
  usecase "项目状态跟踪" as UC7
  usecase "项目统计分析" as UC8
}

Teacher --> UC1
Teacher --> UC2
Teacher --> UC3
Teacher --> UC5
Teacher --> UC6
Teacher --> UC7

Admin --> UC1
Admin --> UC2
Admin --> UC3
Admin --> UC4
Admin --> UC5
Admin --> UC6
Admin --> UC7
Admin --> UC8

UC1 ..> UC5 : <<include>>
UC1 ..> UC6 : <<include>>
UC3 ..> UC5 : <<include>>
UC8 ..> UC2 : <<include>>

@enduml
```

图3.5 项目管理用例图

（4）用例文档
详细的用例文档对用例进行说明，如表3.1所示。

表3.1 项目信息录入用例文档

| 项目 | 内容 |
|------|------|
| 用例名称 | 项目信息录入 |
| 用例编号 | UC-PM-001 |
| 参与者 | 教师用户、管理员 |
| 前置条件 | 用户已登录系统，具有项目管理权限 |
| 后置条件 | 项目信息成功保存到数据库 |
| 主要流程 | 1. 用户点击"新增项目"按钮<br>2. 系统显示项目信息录入页面<br>3. 用户填写项目基本信息（项目名称、类型、级别等）<br>4. 用户添加项目成员并设置贡献率<br>5. 用户上传佐证材料<br>6. 用户点击保存按钮<br>7. 系统验证数据完整性和有效性<br>8. 系统保存项目信息<br>9. 系统显示保存成功提示 |
| 异常流程 | 3a. 必填字段为空：系统提示用户填写必填字段<br>4a. 贡献率总和不为100%：系统提示调整贡献率<br>7a. 数据验证失败：系统显示错误信息，返回编辑页面 |
| 业务规则 | 1. 项目名称不能重复<br>2. 项目成员贡献率总和必须为100%<br>3. 立项时间不能晚于结项时间 |

数据来源：自制

**2. 竞赛管理模块需求分析**

（1）模块概述
记录和管理教师指导学生参加各类学科竞赛的情况，包括竞赛信息、获奖情况、指导教师等信息。

（2）活动图
竞赛管理业务流程如图3.6所示。

```plantuml
@startuml
start
:用户登录系统;
:进入竞赛管理页面;
if (选择操作类型?) then (新增竞赛)
  :点击新增按钮;
  :填写竞赛基本信息;
  :录入获奖信息;
  :添加指导教师;
  :设置指导贡献率;
  :上传获奖证书;
  :保存竞赛信息;
  :系统验证数据;
  if (数据验证通过?) then (是)
    :保存到数据库;
    :显示成功提示;
  else (否)
    :显示错误信息;
    :返回编辑页面;
  endif
elseif (查询竞赛) then
  :输入查询条件;
  :执行查询操作;
  :显示竞赛列表;
elseif (编辑竞赛) then
  :选择要编辑的竞赛;
  :加载竞赛详细信息;
  :修改竞赛信息;
  :保存修改;
else (删除竞赛)
  :选择要删除的竞赛;
  :确认删除操作;
  :执行删除;
  :更新竞赛列表;
endif
stop
@enduml
```

图3.6 竞赛管理活动图

（3）用例图
竞赛管理模块的用例图如图3.7所示。

```plantuml
@startuml
left to right direction
actor "教师用户" as Teacher
actor "管理员" as Admin

rectangle "竞赛管理模块" {
  usecase "竞赛信息录入" as UC1
  usecase "竞赛信息查询" as UC2
  usecase "竞赛信息编辑" as UC3
  usecase "竞赛信息删除" as UC4
  usecase "获奖信息管理" as UC5
  usecase "指导教师管理" as UC6
  usecase "获奖证书上传" as UC7
  usecase "竞赛统计分析" as UC8
}

Teacher --> UC1
Teacher --> UC2
Teacher --> UC3
Teacher --> UC5
Teacher --> UC6
Teacher --> UC7

Admin --> UC1
Admin --> UC2
Admin --> UC3
Admin --> UC4
Admin --> UC5
Admin --> UC6
Admin --> UC7
Admin --> UC8

UC1 ..> UC5 : <<include>>
UC1 ..> UC6 : <<include>>
UC1 ..> UC7 : <<include>>
UC3 ..> UC5 : <<include>>
UC3 ..> UC6 : <<include>>
UC8 ..> UC2 : <<include>>

@enduml
```

图3.7 竞赛管理用例图

（4）用例文档
竞赛信息录入用例文档如表3.2所示。

表3.2 竞赛信息录入用例文档

| 项目 | 内容 |
|------|------|
| 用例名称 | 竞赛信息录入 |
| 用例编号 | UC-CM-001 |
| 参与者 | 教师用户、管理员 |
| 前置条件 | 用户已登录系统，具有竞赛管理权限 |
| 后置条件 | 竞赛信息成功保存到数据库 |
| 主要流程 | 1. 用户点击"新增竞赛"按钮<br>2. 系统显示竞赛信息录入页面<br>3. 用户填写竞赛基本信息（竞赛名称、类型、级别等）<br>4. 用户录入获奖信息（获奖等级、时间、排名）<br>5. 用户添加指导教师并设置贡献率<br>6. 用户上传获奖证书<br>7. 用户点击保存按钮<br>8. 系统验证数据完整性和有效性<br>9. 系统保存竞赛信息<br>10. 系统显示保存成功提示 |
| 异常流程 | 3a. 必填字段为空：系统提示用户填写必填字段<br>5a. 指导教师贡献率总和不为100%：系统提示调整贡献率<br>8a. 数据验证失败：系统显示错误信息，返回编辑页面 |
| 业务规则 | 1. 竞赛名称和获奖时间组合不能重复<br>2. 指导教师贡献率总和必须为100%<br>3. 获奖时间不能晚于当前时间 |

数据来源：自制

**3. 系统管理模块需求分析**

（1）模块概述
提供用户管理、部门管理、角色管理等基础管理功能，为系统运行提供基础支撑。

（2）活动图
系统管理业务流程如图3.8所示。

```plantuml
@startuml
start
:管理员登录系统;
:进入系统管理页面;
if (选择管理类型?) then (用户管理)
  :进入用户管理页面;
  if (操作类型?) then (新增用户)
    :填写用户基本信息;
    :分配用户角色;
    :设置用户状态;
    :保存用户信息;
  elseif (编辑用户) then
    :选择要编辑的用户;
    :修改用户信息;
    :调整用户角色;
    :保存修改;
  else (删除用户)
    :选择要删除的用户;
    :确认删除操作;
    :执行删除;
  endif
elseif (部门管理) then
  :进入部门管理页面;
  if (操作类型?) then (新增部门)
    :填写部门信息;
    :设置上级部门;
    :指定部门负责人;
    :保存部门信息;
  elseif (编辑部门) then
    :选择要编辑的部门;
    :修改部门信息;
    :调整部门层级;
    :保存修改;
  else (删除部门)
    :检查部门是否有下级;
    :检查部门是否有用户;
    if (可以删除?) then (是)
      :执行删除;
    else (否)
      :显示无法删除提示;
    endif
  endif
else (角色管理)
  :进入角色管理页面;
  if (操作类型?) then (新增角色)
    :填写角色信息;
    :分配菜单权限;
    :设置数据权限;
    :保存角色信息;
  elseif (编辑角色) then
    :选择要编辑的角色;
    :修改角色信息;
    :调整权限配置;
    :保存修改;
  else (删除角色)
    :检查角色是否被使用;
    if (可以删除?) then (是)
      :执行删除;
    else (否)
      :显示无法删除提示;
    endif
  endif
endif
stop
@enduml
```

图3.8 系统管理活动图

（3）用例图
系统管理模块的用例图如图3.9所示。

```plantuml
@startuml
left to right direction
actor "系统管理员" as Admin

rectangle "系统管理模块" {
  rectangle "用户管理" {
    usecase "用户信息管理" as UC1
    usecase "用户状态管理" as UC2
    usecase "用户角色分配" as UC3
  }

  rectangle "部门管理" {
    usecase "部门层级管理" as UC4
    usecase "部门信息维护" as UC5
    usecase "负责人指定" as UC6
  }

  rectangle "角色管理" {
    usecase "角色权限管理" as UC7
    usecase "菜单权限配置" as UC8
    usecase "数据权限控制" as UC9
  }
}

Admin --> UC1
Admin --> UC2
Admin --> UC3
Admin --> UC4
Admin --> UC5
Admin --> UC6
Admin --> UC7
Admin --> UC8
Admin --> UC9

UC1 ..> UC3 : <<include>>
UC7 ..> UC8 : <<include>>
UC7 ..> UC9 : <<include>>
UC5 ..> UC6 : <<extend>>

@enduml
```

图3.9 系统管理用例图

（4）用例文档
用户信息管理用例文档如表3.3所示。

表3.3 用户信息管理用例文档

| 项目 | 内容 |
|------|------|
| 用例名称 | 用户信息管理 |
| 用例编号 | UC-SM-001 |
| 参与者 | 系统管理员 |
| 前置条件 | 管理员已登录系统，具有用户管理权限 |
| 后置条件 | 用户信息成功保存到数据库 |
| 主要流程 | 1. 管理员进入用户管理页面<br>2. 点击"新增用户"按钮<br>3. 填写用户基本信息（用户名、昵称、邮箱等）<br>4. 选择用户所属部门<br>5. 分配用户角色<br>6. 设置用户状态（启用/禁用）<br>7. 点击保存按钮<br>8. 系统验证数据有效性<br>9. 系统保存用户信息<br>10. 系统显示保存成功提示 |
| 异常流程 | 3a. 用户名已存在：系统提示用户名重复<br>3b. 邮箱格式错误：系统提示邮箱格式不正确<br>4a. 部门不存在：系统提示选择有效部门<br>8a. 数据验证失败：系统显示错误信息 |
| 业务规则 | 1. 用户名必须唯一<br>2. 邮箱格式必须正确<br>3. 用户必须属于某个部门<br>4. 用户必须分配至少一个角色 |

数据来源：自制

**4. 核心功能模块汇总**

核心功能模块详细描述见表3.4。

表3.4 核心功能模块详细描述

| 功能编号 | 功能名称 | 功能描述 | 优先级 |
|---------|---------|---------|--------|
| 1 | 项目信息管理 | 项目基本信息录入、编辑、删除、查询 | 高 |
| 2 | 项目成员管理 | 项目成员添加、删除、贡献率分配 | 高 |
| 3 | 竞赛信息管理 | 竞赛基本信息录入、编辑、删除、查询 | 高 |
| 4 | 获奖信息管理 | 获奖等级、时间、排名等信息管理 | 高 |
| 5 | 用户信息管理 | 用户基本信息维护、状态管理 | 高 |
| 6 | 部门层级管理 | 多级部门结构创建和维护 | 中 |
| 7 | 角色权限管理 | 角色定义、权限分配、数据权限控制 | 高 |

数据来源：自制

### 3.2 系统开发环境

本系统的开发环境配置如表3.5所示，选择了当前主流且稳定的技术栈，确保系统的可靠性和可维护性。

表3.2 系统开发环境配置

| 类别 | 技术/工具 | 版本 | 说明 |
|------|----------|------|------|
| 操作系统 | Windows 11 | 22H2 | 开发环境操作系统 |
| 开发工具 | IntelliJ IDEA | 2023.2 | Java后端开发IDE |
| 开发工具 | Visual Studio Code | 1.82 | 前端开发IDE |
| 编程语言 | Java | 17 | 后端开发语言 |
| 编程语言 | JavaScript/TypeScript | ES2022 | 前端开发语言 |
| 后端框架 | Spring Boot | 3.1.4 | 微服务开发框架 |
| 前端框架 | Vue3 | 3.3.4 | 前端开发框架 |
| UI组件库 | Element Plus | 2.3.12 | 前端UI组件库 |
| 数据库 | MySQL | 8.0.34 | 关系型数据库 |
| 缓存 | Redis | 7.0.12 | 内存数据库 |
| 构建工具 | Maven | 3.9.4 | Java项目构建工具 |
| 构建工具 | Vite | 4.4.9 | 前端构建工具 |
| 版本控制 | Git | 2.42.0 | 代码版本控制 |

数据来源：自制

### 3.3 系统任务的可行性分析

#### 3.3.1 技术可行性

**1. 技术成熟度分析**

本系统采用的技术栈均为业界成熟且广泛应用的技术：

- **Spring Boot框架**：作为Java生态中最流行的微服务框架，具有完善的文档和社区支持，技术成熟度高
- **Vue3框架**：前端主流框架之一，生态系统完善，学习成本相对较低
- **MySQL数据库**：世界上最流行的开源关系型数据库，稳定性和性能都得到了充分验证
- **Element Plus组件库**：基于Vue3的成熟UI组件库，组件丰富，文档完善

**2. 开发团队技术储备**

开发团队具备以下技术能力：
- 熟练掌握Java语言和Spring Boot框架开发
- 具备Vue3前端开发经验
- 熟悉MySQL数据库设计和优化
- 了解前后端分离架构设计模式

**3. 技术风险评估**

主要技术风险及应对措施：
- **性能风险**：通过合理的数据库设计、缓存策略和代码优化来保证系统性能
- **安全风险**：采用Spring Security框架实现完善的权限控制机制
- **兼容性风险**：选择稳定版本的技术栈，确保各组件间的兼容性

#### 3.3.2 系统安全性分析

**1. 身份认证安全**

系统采用多层次的身份认证机制：
- **用户登录验证**：采用用户名/密码方式，支持密码强度验证
- **会话管理**：基于JWT Token的无状态会话管理
- **登录保护**：实现登录失败锁定机制，防止暴力破解

**2. 权限控制安全**

基于RBAC模型的权限控制体系：
- **角色权限分离**：用户通过角色获得权限，实现权限的统一管理
- **细粒度权限控制**：支持菜单级、接口级、数据级的权限控制
- **数据权限隔离**：通过部门和角色实现数据访问范围控制

**3. 数据安全保护**

多重数据安全保护措施：
- **数据传输安全**：采用HTTPS协议加密数据传输
- **数据存储安全**：敏感数据加密存储，如用户密码采用BCrypt加密
- **数据备份恢复**：定期数据备份，确保数据安全性和完整性
- **SQL注入防护**：使用MyBatis Plus的参数化查询防止SQL注入攻击

**4. 系统监控与审计**

完善的系统监控和审计机制：
- **操作日志记录**：记录用户的关键操作，便于问题追踪和审计
- **异常监控告警**：实时监控系统运行状态，及时发现和处理异常
- **访问控制审计**：记录用户访问行为，防止非法访问

#### 3.3.3 经济可行性

**1. 开发成本分析**

- **人力成本**：项目开发周期约6个月，需要2-3名开发人员
- **硬件成本**：开发和测试环境硬件成本较低
- **软件成本**：主要采用开源技术，软件许可成本低

**2. 运维成本分析**

- **服务器成本**：可部署在云服务器上，成本可控
- **维护成本**：系统采用成熟技术栈，维护成本相对较低
- **升级成本**：模块化设计便于系统升级和功能扩展

**3. 效益分析**

- **管理效率提升**：显著提高教师信息管理效率
- **决策支持价值**：为教育管理决策提供数据支撑
- **长期投资回报**：系统可长期使用，投资回报率高

## 第4章 系统设计

### 4.1 系统技术架构设计

#### 4.1.1 系统软件层次架构

本系统采用分层架构设计模式，结合容器化部署思想，将系统划分为用户界面层、API网关层、应用服务层、数据访问层和基础设施层五个层次。每层都有明确的职责和边界，支持独立部署和扩展。系统架构图如图4.1所示。

```plantuml
@startuml
!theme plain
skinparam backgroundColor #FFFFFF
skinparam packageStyle rectangle
skinparam componentStyle rectangle

' 定义颜色
!define USER_COLOR #E3F2FD
!define GATEWAY_COLOR #F3E5F5
!define APP_COLOR #E8F5E8
!define DATA_COLOR #FFF3E0
!define INFRA_COLOR #FAFAFA

package "用户界面层 (User Interface Layer)" as UILayer <<USER_COLOR>> {

    component "Web浏览器" as Browser {
        port "HTTPS" as BrowserPort
        [用户交互界面]
        [响应式布局]
        [本地存储]
    }

    component "Vue3前端应用容器" as FrontendContainer {
        port "8080" as FrontendPort
        component "路由模块" as Router
        component "状态管理" as Store
        component "UI组件库" as UIComponents
        component "HTTP客户端" as HttpClient

        Router --> Store
        Store --> UIComponents
        UIComponents --> HttpClient
    }
}

package "API网关层 (API Gateway Layer)" as GatewayLayer <<GATEWAY_COLOR>> {

    component "Nginx负载均衡器" as Nginx {
        port "443/80" as NginxPort
        [SSL终止]
        [静态资源服务]
        [负载均衡]
        [反向代理]
    }

    component "Spring Cloud Gateway" as APIGateway {
        port "8888" as GatewayPort
        [请求路由]
        [限流熔断]
        [统一认证]
        [跨域处理]
        [API监控]
    }
}

package "应用服务层 (Application Service Layer)" as AppLayer <<APP_COLOR>> {

    component "项目管理服务" as ProjectService {
        port "8081" as ProjectPort
        [项目CRUD操作]
        [项目成员管理]
        [项目状态跟踪]
        [项目统计分析]
    }

    component "竞赛管理服务" as CompetitionService {
        port "8082" as CompetitionPort
        [竞赛信息管理]
        [获奖信息管理]
        [指导教师管理]
        [竞赛统计分析]
    }

    component "系统管理服务" as SystemService {
        port "8083" as SystemPort
        [用户管理]
        [部门管理]
        [角色权限管理]
        [系统配置]
    }

    component "认证授权服务" as AuthService {
        port "8084" as AuthPort
        [JWT认证]
        [权限验证]
        [单点登录]
        [会话管理]
    }

    component "文件服务" as FileService {
        port "8085" as FilePort
        [文件上传下载]
        [文件存储管理]
        [文件访问控制]
        [文件格式验证]
    }
}

package "数据访问层 (Data Access Layer)" as DataLayer <<DATA_COLOR>> {

    component "数据访问组件" as DataAccess {
        [MyBatis Plus ORM]
        [数据源管理]
        [事务管理]
        [连接池管理]
        [SQL监控]
    }

    component "缓存访问组件" as CacheAccess {
        [Redis客户端]
        [缓存策略]
        [分布式锁]
        [会话存储]
    }
}

package "基础设施层 (Infrastructure Layer)" as InfraLayer <<INFRA_COLOR>> {

    database "MySQL主从集群" as MySQLCluster {
        [主数据库 (Master)]
        [从数据库 (Slave)]
        [读写分离]
        [数据备份]
    }

    database "Redis集群" as RedisCluster {
        [Redis主节点]
        [Redis从节点]
        [哨兵模式]
        [持久化存储]
    }

    storage "分布式文件存储" as FileStorage {
        [MinIO对象存储]
        [文件分片存储]
        [冗余备份]
        [CDN加速]
    }

    component "监控告警系统" as Monitor {
        [应用性能监控]
        [日志收集分析]
        [系统资源监控]
        [告警通知]
    }
}

' 连接关系
Browser::BrowserPort --> FrontendContainer::FrontendPort : "用户访问"
FrontendContainer --> Nginx::NginxPort : "HTTPS请求"
Nginx --> APIGateway::GatewayPort : "请求转发"

APIGateway --> ProjectService::ProjectPort : "项目管理请求"
APIGateway --> CompetitionService::CompetitionPort : "竞赛管理请求"
APIGateway --> SystemService::SystemPort : "系统管理请求"
APIGateway --> AuthService::AuthPort : "认证授权请求"
APIGateway --> FileService::FilePort : "文件服务请求"

ProjectService --> DataAccess : "数据操作"
CompetitionService --> DataAccess : "数据操作"
SystemService --> DataAccess : "数据操作"
AuthService --> CacheAccess : "会话缓存"
FileService --> FileStorage : "文件存储"

DataAccess --> MySQLCluster : "数据持久化"
CacheAccess --> RedisCluster : "缓存操作"

ProjectService --> Monitor : "性能监控"
CompetitionService --> Monitor : "性能监控"
SystemService --> Monitor : "性能监控"
AuthService --> Monitor : "性能监控"
FileService --> Monitor : "性能监控"

@enduml
```

图4.1 系统软件层次架构图

**各层职责说明：**

1. **表示层**：负责用户交互和数据展示，采用Vue3框架构建响应式用户界面
2. **网关层**：提供统一的API入口，处理请求路由、认证、限流等横切关注点
3. **业务逻辑层**：实现核心业务逻辑，包括控制器、服务和安全控制
4. **数据访问层**：封装数据访问逻辑，提供统一的数据操作接口
5. **数据存储层**：负责数据持久化，包括关系型数据库、缓存和文件存储

#### 4.1.2 系统功能架构

系统功能架构按照业务领域进行模块划分，功能结构图如图4.2所示。

```plantuml
@startuml
package "数字画像系统功能架构" {

    package "用户交互层" {
        rectangle "Web前端界面" as WebUI {
            + 项目管理界面
            + 竞赛管理界面
            + 系统管理界面
            + 数据分析界面
            + 个人中心界面
        }

        rectangle "移动端界面" as MobileUI {
            + 响应式设计
            + 移动端适配
            + 触摸操作优化
        }
    }

    package "核心业务层" {
        rectangle "项目管理模块" as ProjectModule {
            + 项目信息管理
            + 项目成员管理
            + 项目进度跟踪
            + 项目统计分析
            + 佐证材料管理
        }

        rectangle "竞赛管理模块" as CompetitionModule {
            + 竞赛信息管理
            + 获奖信息管理
            + 指导教师管理
            + 竞赛统计分析
            + 获奖证书管理
        }

        rectangle "用户管理模块" as UserModule {
            + 用户信息管理
            + 用户状态管理
            + 用户角色分配
            + 用户权限控制
        }

        rectangle "部门管理模块" as DeptModule {
            + 部门层级管理
            + 部门信息维护
            + 负责人指定
            + 部门统计分析
        }

        rectangle "角色管理模块" as RoleModule {
            + 角色权限管理
            + 菜单权限配置
            + 数据权限控制
            + 角色分配管理
        }
    }

    package "数据分析层" {
        rectangle "统计分析引擎" as AnalysisEngine {
            + 项目数据统计
            + 竞赛数据统计
            + 趋势分析算法
            + 画像生成算法
        }

        rectangle "报表生成器" as ReportGenerator {
            + 动态报表生成
            + 图表可视化
            + 数据导出功能
            + 报表模板管理
        }
    }

    package "基础服务层" {
        rectangle "认证授权服务" as AuthService {
            + JWT身份认证
            + RBAC权限控制
            + 单点登录支持
            + 会话管理
        }

        rectangle "文件服务" as FileService {
            + 文件上传下载
            + 文件存储管理
            + 文件访问控制
            + 文件格式验证
        }

        rectangle "缓存服务" as CacheService {
            + 数据缓存管理
            + 会话缓存
            + 查询结果缓存
            + 缓存失效策略
        }

        rectangle "日志服务" as LogService {
            + 操作日志记录
            + 错误日志管理
            + 性能监控日志
            + 日志分析统计
        }
    }
}

WebUI --> ProjectModule
WebUI --> CompetitionModule
WebUI --> UserModule
WebUI --> DeptModule
WebUI --> RoleModule

MobileUI --> ProjectModule
MobileUI --> CompetitionModule

ProjectModule --> AnalysisEngine
CompetitionModule --> AnalysisEngine
AnalysisEngine --> ReportGenerator

ProjectModule --> AuthService
CompetitionModule --> AuthService
UserModule --> AuthService
DeptModule --> AuthService
RoleModule --> AuthService

ProjectModule --> FileService
CompetitionModule --> FileService

ProjectModule --> CacheService
CompetitionModule --> CacheService
UserModule --> CacheService

AuthService --> LogService
FileService --> LogService
CacheService --> LogService

@enduml
```

图4.2 系统功能结构图

### 4.2 系统功能设计

#### 4.2.1 项目管理模块设计

**1. 时序图**

项目信息录入的时序图如图4.3所示，展示了用户、前端、后端服务和数据库之间的交互过程。

```plantuml
@startuml
participant "用户" as User
participant "前端界面" as Frontend
participant "项目控制器" as Controller
participant "项目服务" as Service
participant "权限服务" as AuthService
participant "数据访问层" as DAO
participant "数据库" as DB

User -> Frontend: 点击新增项目
Frontend -> Frontend: 显示项目录入表单
User -> Frontend: 填写项目信息
User -> Frontend: 点击保存按钮

Frontend -> Controller: POST /api/projects
Controller -> AuthService: 验证用户权限
AuthService -> Controller: 权限验证通过

Controller -> Service: createProject(projectData)
Service -> Service: 数据验证和处理
Service -> DAO: insertProject(project)
DAO -> DB: INSERT INTO projects
DB -> DAO: 返回插入结果
DAO -> Service: 返回项目ID

Service -> DAO: insertProjectMembers(members)
DAO -> DB: INSERT INTO project_members
DB -> DAO: 返回插入结果
DAO -> Service: 返回成员信息

Service -> Controller: 返回创建结果
Controller -> Frontend: 返回成功响应
Frontend -> User: 显示保存成功提示

@enduml
```

图4.3 项目信息录入时序图

**2. 程序流程图**

项目管理的核心业务流程如图4.4所示。

```plantuml
@startuml
start
:用户访问项目管理页面;
:系统验证用户权限;
if (权限验证通过?) then (是)
  :显示项目管理界面;
  :用户选择操作类型;
  if (操作类型?) then (新增项目)
    :显示项目录入表单;
    :用户填写项目信息;
    :验证表单数据;
    if (数据验证通过?) then (是)
      :保存项目信息;
      :保存项目成员;
      :上传佐证材料;
      :显示保存成功;
    else (否)
      :显示错误信息;
      :返回表单页面;
    endif
  elseif (查询项目) then
    :输入查询条件;
    :执行数据库查询;
    :显示查询结果;
  elseif (编辑项目) then
    :选择要编辑的项目;
    :加载项目详细信息;
    :修改项目信息;
    :保存修改结果;
  else (删除项目)
    :选择要删除的项目;
    :确认删除操作;
    :执行删除操作;
    :更新项目列表;
  endif
else (否)
  :显示权限不足提示;
  :跳转到登录页面;
endif
stop
@enduml
```

图4.4 项目管理程序流程图

**3. 类图设计**

项目管理模块的类图设计如图4.5所示。

```plantuml
@startuml
package "项目管理模块" {
    class ProjectController {
        - projectService: ProjectService
        + createProject(reqVO): CommonResult<Integer>
        + updateProject(reqVO): CommonResult<Boolean>
        + deleteProject(id): CommonResult<Boolean>
        + getProject(id): CommonResult<ProjectRespVO>
        + getProjectPage(pageReqVO): CommonResult<PageResult>
    }

    class ProjectService {
        - projectMapper: ProjectMapper
        - fileService: FileService
        + createProject(createReqVO): Integer
        + updateProject(updateReqVO): void
        + deleteProject(id): void
        + getProject(id): ProjectDO
        + getProjectPage(pageReqVO): PageResult<ProjectDO>
        - validateProjectData(reqVO): void
    }

    class ProjectMapper {
        + insert(project): int
        + updateById(project): int
        + deleteById(id): int
        + selectById(id): ProjectDO
        + selectPage(pageReqVO): PageResult<ProjectDO>
    }

    class ProjectDO {
        - id: Integer
        - name: String
        - type: Integer
        - organizer: Integer
        - level: Integer
        - author: String
        - member: String
        - rate: String
        - projectStartTime: LocalDateTime
        - projectEndTime: LocalDateTime
        - status: Integer
        - evidenceUrl: List<String>
        - remark: String
    }

    class ProjectSaveReqVO {
        - id: Integer
        - name: String
        - type: Integer
        - organizer: Integer
        - level: Integer
        - author: String
        - member: String
        - rate: String
        - projectStartTime: LocalDateTime
        - projectEndTime: LocalDateTime
        - status: Integer
        - evidenceUrl: List<String>
        - remark: String
    }

    class ProjectRespVO {
        - id: Integer
        - name: String
        - typeName: String
        - organizerName: String
        - levelName: String
        - author: String
        - member: String
        - rate: String
        - projectStartTime: LocalDateTime
        - projectEndTime: LocalDateTime
        - statusName: String
        - evidenceUrl: List<String>
        - remark: String
        - createTime: LocalDateTime
    }
}

ProjectController --> ProjectService
ProjectService --> ProjectMapper
ProjectMapper --> ProjectDO
ProjectController ..> ProjectSaveReqVO
ProjectController ..> ProjectRespVO
ProjectService ..> ProjectDO

@enduml
```

图4.5 项目管理模块类图

#### 4.2.2 竞赛管理模块设计

**1. 时序图**

竞赛信息管理的时序图如图4.6所示。

```plantuml
@startuml
participant "用户" as User
participant "前端界面" as Frontend
participant "竞赛控制器" as Controller
participant "竞赛服务" as Service
participant "文件服务" as FileService
participant "数据访问层" as DAO
participant "数据库" as DB

User -> Frontend: 录入竞赛信息
Frontend -> Controller: POST /api/competition
Controller -> Service: createCompetition(competitionData)

Service -> Service: 验证竞赛数据
Service -> DAO: insertCompetition(competition)
DAO -> DB: INSERT INTO competition
DB -> DAO: 返回竞赛ID

Service -> DAO: insertAwardInfo(awardInfo)
DAO -> DB: INSERT INTO award_info
DB -> DAO: 返回获奖信息

alt 上传获奖证书
    Service -> FileService: uploadCertificate(file)
    FileService -> Service: 返回文件URL
    Service -> DAO: updateCertificateUrl(url)
end

Service -> Controller: 返回创建结果
Controller -> Frontend: 返回成功响应
Frontend -> User: 显示录入成功

@enduml
```

图4.6 竞赛信息管理时序图

#### 4.2.3 系统管理模块设计

**1. 用户管理类图**

用户管理模块的类图设计如图4.7所示。

```plantuml
@startuml
package "用户管理模块" {
    class UserController {
        - userService: UserService
        + createUser(reqVO): CommonResult<Integer>
        + updateUser(reqVO): CommonResult<Boolean>
        + deleteUser(id): CommonResult<Boolean>
        + getUser(id): CommonResult<UserRespVO>
        + getUserPage(pageReqVO): CommonResult<PageResult>
        + resetPassword(id): CommonResult<Boolean>
    }

    class UserService {
        - userMapper: UserMapper
        - roleService: RoleService
        - deptService: DeptService
        + createUser(createReqVO): Integer
        + updateUser(updateReqVO): void
        + deleteUser(id): void
        + getUser(id): UserDO
        + getUserPage(pageReqVO): PageResult<UserDO>
        + resetPassword(id): void
        - validateUserData(reqVO): void
    }

    class UserDO {
        - id: Integer
        - username: String
        - nickname: String
        - email: String
        - mobile: String
        - sex: Integer
        - avatar: String
        - password: String
        - status: Integer
        - deptId: Integer
        - roleIds: Set<Integer>
        - loginIp: String
        - loginDate: LocalDateTime
    }

    class UserSaveReqVO {
        - id: Integer
        - username: String
        - nickname: String
        - email: String
        - mobile: String
        - sex: Integer
        - deptId: Integer
        - roleIds: Set<Integer>
        - status: Integer
    }
}

UserController --> UserService
UserService --> UserDO
UserController ..> UserSaveReqVO

@enduml
```

图4.7 用户管理模块类图

### 4.3 数据库设计

#### 4.3.1 实体-联系图

系统的实体关系图如图4.8所示，展示了各个实体之间的关联关系。

```plantuml
@startuml
!define ENTITY entity
!define RELATIONSHIP diamond

ENTITY "用户(User)" as User {
    * user_id : INTEGER
    --
    username : VARCHAR(50)
    nickname : VARCHAR(50)
    email : VARCHAR(100)
    mobile : VARCHAR(20)
    password : VARCHAR(100)
    status : INTEGER
    dept_id : INTEGER
    create_time : DATETIME
    update_time : DATETIME
}

ENTITY "部门(Department)" as Dept {
    * dept_id : INTEGER
    --
    dept_name : VARCHAR(50)
    parent_id : INTEGER
    sort : INTEGER
    leader_id : INTEGER
    phone : VARCHAR(20)
    email : VARCHAR(100)
    status : INTEGER
    create_time : DATETIME
}

ENTITY "角色(Role)" as Role {
    * role_id : INTEGER
    --
    role_name : VARCHAR(50)
    role_key : VARCHAR(100)
    role_sort : INTEGER
    data_scope : INTEGER
    status : INTEGER
    remark : VARCHAR(500)
    create_time : DATETIME
}

ENTITY "用户角色关联(UserRole)" as UserRole {
    * user_id : INTEGER
    * role_id : INTEGER
    --
    create_time : DATETIME
}

ENTITY "项目(Project)" as Project {
    * project_id : INTEGER
    --
    name : VARCHAR(200)
    type : INTEGER
    organizer : INTEGER
    organizer_other : VARCHAR(100)
    level : INTEGER
    author : VARCHAR(100)
    member : TEXT
    rate : VARCHAR(500)
    project_start_time : DATETIME
    project_end_time : DATETIME
    status : INTEGER
    evidence_url : JSON
    remark : TEXT
    creator : INTEGER
    create_time : DATETIME
    update_time : DATETIME
}

ENTITY "竞赛(Competition)" as Competition {
    * competition_id : INTEGER
    --
    name : VARCHAR(200)
    type : INTEGER
    organizer : INTEGER
    organizer_other : VARCHAR(100)
    level : INTEGER
    award_level : INTEGER
    award_time : DATETIME
    instructor : VARCHAR(100)
    instructor_rate : VARCHAR(500)
    student_names : TEXT
    certificate_url : JSON
    remark : TEXT
    creator : INTEGER
    create_time : DATETIME
    update_time : DATETIME
}

ENTITY "字典类型(DictType)" as DictType {
    * dict_id : INTEGER
    --
    dict_name : VARCHAR(100)
    dict_type : VARCHAR(100)
    status : INTEGER
    remark : VARCHAR(500)
    create_time : DATETIME
}

ENTITY "字典数据(DictData)" as DictData {
    * dict_code : INTEGER
    --
    dict_sort : INTEGER
    dict_label : VARCHAR(100)
    dict_value : VARCHAR(100)
    dict_type : VARCHAR(100)
    css_class : VARCHAR(100)
    list_class : VARCHAR(100)
    is_default : BOOLEAN
    status : INTEGER
    remark : VARCHAR(500)
    create_time : DATETIME
}

' 关系定义
User ||--o{ UserRole : "用户拥有角色"
Role ||--o{ UserRole : "角色分配给用户"
Dept ||--o{ User : "部门包含用户"
User ||--o{ Project : "用户创建项目"
User ||--o{ Competition : "用户创建竞赛"
DictType ||--o{ DictData : "字典类型包含字典数据"

@enduml
```

图4.8 系统实体关系图

#### 4.3.2 数据库表的设计

**1. 用户管理相关表**

（1）用户表(system_users)

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 说明 |
|--------|----------|------|----------|------|------|
| id | INT | 11 | NOT NULL | PK | 用户ID |
| username | VARCHAR | 50 | NOT NULL | | 用户名 |
| nickname | VARCHAR | 50 | NULL | | 昵称 |
| email | VARCHAR | 100 | NULL | | 邮箱 |
| mobile | VARCHAR | 20 | NULL | | 手机号 |
| sex | TINYINT | 1 | NULL | | 性别(0女1男) |
| avatar | VARCHAR | 500 | NULL | | 头像URL |
| password | VARCHAR | 100 | NOT NULL | | 密码 |
| status | TINYINT | 1 | NOT NULL | | 状态(0禁用1启用) |
| dept_id | INT | 11 | NULL | | 部门ID |
| login_ip | VARCHAR | 50 | NULL | | 最后登录IP |
| login_date | DATETIME | | NULL | | 最后登录时间 |
| creator | VARCHAR | 64 | NULL | | 创建者 |
| create_time | DATETIME | | NOT NULL | | 创建时间 |
| updater | VARCHAR | 64 | NULL | | 更新者 |
| update_time | DATETIME | | NOT NULL | | 更新时间 |
| deleted | BIT | 1 | NOT NULL | | 是否删除 |
| tenant_id | BIGINT | 20 | NOT NULL | | 租户编号 |

（2）部门表(system_dept)

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 说明 |
|--------|----------|------|----------|------|------|
| id | BIGINT | 20 | NOT NULL | PK | 部门ID |
| name | VARCHAR | 50 | NOT NULL | | 部门名称 |
| parent_id | BIGINT | 20 | NOT NULL | | 父部门ID |
| sort | INT | 11 | NOT NULL | | 显示顺序 |
| leader_user_id | BIGINT | 20 | NULL | | 负责人用户ID |
| phone | VARCHAR | 11 | NULL | | 联系电话 |
| email | VARCHAR | 50 | NULL | | 邮箱 |
| status | TINYINT | 1 | NOT NULL | | 状态(0禁用1启用) |
| creator | VARCHAR | 64 | NULL | | 创建者 |
| create_time | DATETIME | | NOT NULL | | 创建时间 |
| updater | VARCHAR | 64 | NULL | | 更新者 |
| update_time | DATETIME | | NOT NULL | | 更新时间 |
| deleted | BIT | 1 | NOT NULL | | 是否删除 |
| tenant_id | BIGINT | 20 | NOT NULL | | 租户编号 |

（3）角色表(system_role)

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 说明 |
|--------|----------|------|----------|------|------|
| id | BIGINT | 20 | NOT NULL | PK | 角色ID |
| name | VARCHAR | 30 | NOT NULL | | 角色名称 |
| code | VARCHAR | 100 | NOT NULL | | 角色权限字符串 |
| sort | INT | 11 | NOT NULL | | 显示顺序 |
| data_scope | TINYINT | 1 | NOT NULL | | 数据范围 |
| data_scope_dept_ids | VARCHAR | 500 | NOT NULL | | 数据范围部门数组 |
| status | TINYINT | 1 | NOT NULL | | 状态(0禁用1启用) |
| type | TINYINT | 1 | NOT NULL | | 角色类型 |
| remark | VARCHAR | 500 | NULL | | 备注 |
| creator | VARCHAR | 64 | NULL | | 创建者 |
| create_time | DATETIME | | NOT NULL | | 创建时间 |
| updater | VARCHAR | 64 | NULL | | 更新者 |
| update_time | DATETIME | | NOT NULL | | 更新时间 |
| deleted | BIT | 1 | NOT NULL | | 是否删除 |
| tenant_id | BIGINT | 20 | NOT NULL | | 租户编号 |

**2. 业务数据相关表**

（1）项目表(digital_projects)

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 说明 |
|--------|----------|------|----------|------|------|
| id | INT | 11 | NOT NULL | PK | 项目ID |
| name | VARCHAR | 200 | NOT NULL | | 项目名称 |
| type | INT | 11 | NULL | | 项目类型 |
| organizer | INT | 11 | NULL | | 主办方 |
| organizer_other | VARCHAR | 100 | NULL | | 其他主办方 |
| level | INT | 11 | NULL | | 项目级别 |
| author | VARCHAR | 100 | NULL | | 第一作者/负责人 |
| member | TEXT | | NULL | | 项目成员 |
| rate | VARCHAR | 500 | NULL | | 贡献率数组 |
| project_start_time | DATETIME | | NULL | | 立项时间 |
| project_end_time | DATETIME | | NULL | | 结项时间 |
| status | INT | 11 | NULL | | 项目状态 |
| evidence_url | JSON | | NULL | | 佐证材料URL |
| remark | TEXT | | NULL | | 备注 |
| creator | VARCHAR | 64 | NULL | | 创建者 |
| create_time | DATETIME | | NOT NULL | | 创建时间 |
| updater | VARCHAR | 64 | NULL | | 更新者 |
| update_time | DATETIME | | NOT NULL | | 更新时间 |
| deleted | BIT | 1 | NOT NULL | | 是否删除 |
| tenant_id | BIGINT | 20 | NOT NULL | | 租户编号 |

（2）竞赛表(digital_competition)

| 字段名 | 数据类型 | 长度 | 是否为空 | 主键 | 说明 |
|--------|----------|------|----------|------|------|
| id | INT | 11 | NOT NULL | PK | 竞赛ID |
| name | VARCHAR | 200 | NOT NULL | | 竞赛名称 |
| type | INT | 11 | NULL | | 竞赛类型 |
| organizer | INT | 11 | NULL | | 主办方 |
| organizer_other | VARCHAR | 100 | NULL | | 其他主办方 |
| level | INT | 11 | NULL | | 竞赛级别 |
| award_level | INT | 11 | NULL | | 获奖等级 |
| award_time | DATETIME | | NULL | | 获奖时间 |
| instructor | VARCHAR | 100 | NULL | | 指导教师 |
| instructor_rate | VARCHAR | 500 | NULL | | 指导教师贡献率 |
| student_names | TEXT | | NULL | | 参赛学生姓名 |
| certificate_url | JSON | | NULL | | 获奖证书URL |
| remark | TEXT | | NULL | | 备注 |
| creator | VARCHAR | 64 | NULL | | 创建者 |
| create_time | DATETIME | | NOT NULL | | 创建时间 |
| updater | VARCHAR | 64 | NULL | | 更新者 |
| update_time | DATETIME | | NOT NULL | | 更新时间 |
| deleted | BIT | 1 | NOT NULL | | 是否删除 |
| tenant_id | BIGINT | 20 | NOT NULL | | 租户编号 |

### 4.4 本章小结

本章详细介绍了数字画像系统的设计方案，主要包括以下几个方面：

1. **系统技术架构设计**：采用分层架构模式，将系统划分为表示层、业务逻辑层、数据访问层和数据存储层，确保了系统的可维护性和可扩展性。

2. **系统功能设计**：通过时序图、程序流程图和类图详细描述了项目管理、竞赛管理和系统管理等核心模块的设计方案，明确了各模块的职责和交互关系。

3. **数据库设计**：通过实体关系图展示了系统各实体之间的关联关系，并详细设计了数据库表结构，为系统的数据存储提供了完整的方案。

整个设计方案遵循软件工程的设计原则，采用成熟的技术架构和设计模式，确保了系统的技术先进性、安全可靠性和可维护性。为后续的系统实现奠定了坚实的基础。
- **Spring Boot 3.x**：提供依赖注入、自动配置等核心功能
- **Spring Security**：实现身份认证和权限控制
- **MyBatis Plus**：简化数据访问层开发，提供代码生成功能
- **MySQL 8.0**：关系型数据库，存储业务数据
- **Redis**：缓存中间件，提升系统性能
- **Jackson**：JSON序列化和反序列化处理

**前端技术栈：**
- **Vue3 + Composition API**：现代化的响应式前端框架
- **TypeScript**：提供类型安全和更好的开发体验
- **Element Plus**：企业级UI组件库
- **Vite**：快速的前端构建工具
- **Pinia**：Vue3官方推荐的状态管理库
- **Axios**：HTTP客户端，处理API请求

#### 4.2.3 设计模式应用

1. **MVC模式**：分离业务逻辑、数据访问和视图展示
2. **Repository模式**：封装数据访问逻辑，提供统一的数据操作接口
3. **DTO模式**：定义数据传输对象，确保数据传输的安全性和一致性
4. **组件化模式**：前端采用组件化开发，提高代码复用性
5. **策略模式**：在权限控制中应用，支持多种权限验证策略

### 4.3 系统的功能结构设计

#### 4.3.1 功能模块划分

系统功能结构按照业务领域进行模块划分，如图4.2所示。

```plantuml
@startuml
!define RECTANGLE class

package "教师数字画像系统功能结构" {

    package "核心业务功能" {
        RECTANGLE "项目管理" as ProjectMgmt {
            + 项目信息录入
            + 项目成员管理
            + 项目状态跟踪
            + 项目统计分析
            + 佐证材料管理
        }

        RECTANGLE "竞赛管理" as CompetitionMgmt {
            + 竞赛信息录入
            + 获奖信息管理
            + 指导教师管理
            + 竞赛统计分析
            + 竞赛分类管理
        }
    }

    package "系统管理功能" {
        RECTANGLE "用户管理" as UserMgmt {
            + 用户信息维护
            + 用户状态管理
            + 密码安全管理
            + 登录记录查询
            + 用户角色分配
        }

        RECTANGLE "部门管理" as DeptMgmt {
            + 部门信息维护
            + 部门层级管理
            + 部门关系维护
            + 负责人指定
            + 部门权限设置
        }

        RECTANGLE "角色管理" as RoleMgmt {
            + 角色信息维护
            + 权限分配管理
            + 数据权限控制
            + 菜单权限配置
            + 角色状态管理
        }
    }

    package "基础支撑功能" {
        RECTANGLE "权限控制" as PermissionCtrl {
            + 登录认证
            + 权限验证
            + 数据权限过滤
            + 操作权限控制
        }

        RECTANGLE "文件管理" as FileMgmt {
            + 文件上传
            + 文件下载
            + 文件预览
            + 文件删除
        }

        RECTANGLE "系统监控" as SystemMonitor {
            + 操作日志
            + 系统日志
            + 性能监控
            + 异常告警
        }
    }
}

ProjectMgmt ..> PermissionCtrl : 使用
CompetitionMgmt ..> PermissionCtrl : 使用
UserMgmt ..> PermissionCtrl : 使用
DeptMgmt ..> PermissionCtrl : 使用
RoleMgmt ..> PermissionCtrl : 使用

ProjectMgmt ..> FileMgmt : 使用
CompetitionMgmt ..> FileMgmt : 使用

ProjectMgmt ..> SystemMonitor : 记录
CompetitionMgmt ..> SystemMonitor : 记录
UserMgmt ..> SystemMonitor : 记录

@enduml
```

图4.2 系统功能结构图

#### 4.3.2 模块间关系设计

**1. 核心业务模块**
- 项目管理模块：实现项目信息管理和项目成员管理两个核心功能
- 竞赛管理模块：实现竞赛信息管理和获奖信息管理两个核心功能
- 两个模块相对独立，但都依赖于系统管理模块提供的基础数据支撑

**2. 系统管理模块**
- 用户信息管理：提供系统用户的基础信息维护和状态管理
- 部门层级管理：支持多级部门结构的创建和维护
- 角色权限管理：实现角色定义、权限分配和数据权限控制
- 三个管理功能构成完整的权限管理体系

**3. 基础支撑模块**
- 为所有业务模块提供统一的基础服务
- 权限控制模块确保系统安全性，支持细粒度的权限控制
- 文件管理模块支持佐证材料的存储和管理
- 系统监控模块提供日志记录和性能监控功能

### 4.4 系统控制流程

#### 4.4.1 用户登录流程

用户登录是系统的入口流程，如图4.3所示。

```plantuml
@startuml
actor 用户 as User
participant "前端界面" as Frontend
participant "API网关" as Gateway
participant "用户服务" as UserService
participant "Redis缓存" as Redis
participant "MySQL数据库" as MySQL

User -> Frontend: 输入用户名密码
Frontend -> Gateway: 发送登录请求
Gateway -> UserService: 转发登录请求
UserService -> MySQL: 验证用户信息
MySQL -> UserService: 返回用户数据
UserService -> UserService: 生成JWT Token
UserService -> Redis: 存储用户会话
UserService -> Gateway: 返回登录结果
Gateway -> Frontend: 返回Token和用户信息
Frontend -> User: 显示登录成功，跳转主页

@enduml
```

图4.3 用户登录流程图

#### 4.4.2 权限验证流程

系统的权限验证流程确保用户只能访问被授权的功能，如图4.4所示。

```plantuml
@startuml
actor 用户 as User
participant "前端界面" as Frontend
participant "API网关" as Gateway
participant "权限服务" as PermissionService
participant "业务服务" as BusinessService
participant "Redis缓存" as Redis

User -> Frontend: 访问功能页面
Frontend -> Gateway: 发送API请求（携带Token）
Gateway -> PermissionService: 验证Token有效性
PermissionService -> Redis: 查询用户会话
Redis -> PermissionService: 返回用户信息
PermissionService -> PermissionService: 检查用户权限
alt 权限验证通过
    PermissionService -> Gateway: 权限验证成功
    Gateway -> BusinessService: 转发业务请求
    BusinessService -> Gateway: 返回业务数据
    Gateway -> Frontend: 返回响应数据
    Frontend -> User: 显示页面内容
else 权限验证失败
    PermissionService -> Gateway: 权限验证失败
    Gateway -> Frontend: 返回权限错误
    Frontend -> User: 显示无权限提示
end

@enduml
```

图4.4 权限验证流程图

#### 4.4.3 数据操作流程

以项目管理为例，展示典型的数据操作流程，如图4.5所示。

```plantuml
@startuml
actor 用户 as User
participant "前端界面" as Frontend
participant "项目服务" as ProjectService
participant "权限服务" as PermissionService
participant "文件服务" as FileService
participant "MySQL数据库" as MySQL

User -> Frontend: 填写项目信息
Frontend -> ProjectService: 提交项目数据
ProjectService -> PermissionService: 验证操作权限
PermissionService -> ProjectService: 权限验证通过
ProjectService -> ProjectService: 数据验证和处理
alt 包含文件上传
    ProjectService -> FileService: 处理文件上传
    FileService -> ProjectService: 返回文件URL
end
ProjectService -> MySQL: 保存项目数据
MySQL -> ProjectService: 返回保存结果
ProjectService -> Frontend: 返回操作结果
Frontend -> User: 显示操作成功信息

@enduml
```

图4.5 数据操作流程图

## 第5章 系统实现

### 5.1 后台管理端

#### 5.1.1 "项目管理"功能界面实现

项目管理模块是数字画像系统的核心功能之一，主要负责教师科研项目和教学项目的全生命周期管理。该模块采用Vue3 + Element Plus技术栈实现，提供了直观友好的用户界面。

**1. 项目列表页面**

项目列表页面是项目管理的主入口，提供项目的查询、新增、编辑、删除等功能。页面采用表格形式展示项目信息，支持分页显示和条件筛选。

主要功能特点：
- 支持按项目名称、项目类型、项目级别等条件进行筛选查询
- 提供项目状态的可视化显示，通过不同颜色标识项目进展状态
- 支持批量操作，可同时删除多个项目记录
- 集成权限控制，根据用户角色显示相应的操作按钮

**2. 项目新增/编辑页面**

项目新增和编辑页面采用表单布局，将项目信息分为基本信息、成员信息、佐证材料三个部分，便于用户分步填写。

表单验证规则：
- 项目名称：必填，长度不超过200字符，不允许重复
- 项目类型：必选，从预设字典中选择
- 立项时间：必填，不能晚于结项时间
- 项目成员：支持动态添加，贡献率总和必须为100%
- 佐证材料：支持多文件上传，限制文件格式和大小

**3. 项目详情页面**

项目详情页面以只读形式展示项目的完整信息，包括项目基本信息、成员列表、佐证材料等。页面支持打印功能，便于生成项目报告。

**4. 核心代码实现**

项目数据模型定义：

```javascript
// 项目数据模型
export interface ProjectForm {
  id?: number
  name: string                    // 项目名称
  type: number                   // 项目类型
  organizer: number              // 主办方
  organizerOther?: string        // 其他主办方
  level: number                  // 项目级别
  author: string                 // 第一作者/负责人
  member: string                 // 项目成员
  rate: string                   // 贡献率数组
  projectStartTime: Date         // 立项时间
  projectEndTime?: Date          // 结项时间
  status: number                 // 项目状态
  evidenceUrl: string[]          // 佐证材料URL
  remark?: string                // 备注
}
```

项目列表组件核心逻辑：

```vue
<template>
  <div class="project-management">
    <!-- 查询条件 -->
    <el-form :model="queryForm" inline>
      <el-form-item label="项目名称">
        <el-input v-model="queryForm.name" placeholder="请输入项目名称" />
      </el-form-item>
      <el-form-item label="项目类型">
        <el-select v-model="queryForm.type" placeholder="请选择项目类型">
          <el-option v-for="item in projectTypes" :key="item.value"
                     :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">查询</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮 -->
    <el-row class="mb-3">
      <el-button type="primary" @click="handleAdd">新增项目</el-button>
      <el-button type="danger" @click="handleBatchDelete">批量删除</el-button>
    </el-row>

    <!-- 项目列表表格 -->
    <el-table :data="projectList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column prop="name" label="项目名称" min-width="200" />
      <el-table-column prop="typeName" label="项目类型" width="120" />
      <el-table-column prop="levelName" label="项目级别" width="120" />
      <el-table-column prop="author" label="负责人" width="120" />
      <el-table-column prop="statusName" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusType(row.status)">{{ row.statusName }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="180" />
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
          <el-button type="info" size="small" @click="handleView(row)">详情</el-button>
          <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页组件 -->
    <el-pagination
      v-model:current-page="queryForm.pageNo"
      v-model:page-size="queryForm.pageSize"
      :total="total"
      @current-change="getProjectList"
      @size-change="getProjectList"
    />
  </div>
</template>
```

#### 5.1.2 "竞赛管理"功能界面实现

竞赛管理模块负责管理教师指导学生参加各类学科竞赛的信息，包括竞赛基本信息、获奖情况、指导教师等数据的录入和维护。

**1. 竞赛列表页面**

竞赛列表页面展示所有竞赛记录，支持按竞赛名称、竞赛类型、获奖等级等条件进行筛选。页面设计注重数据的可视化展示，通过图标和颜色区分不同的获奖等级。

主要功能特点：
- 支持多维度筛选查询，包括时间范围、竞赛级别、获奖等级等
- 获奖等级采用徽章样式显示，直观展示获奖情况
- 支持竞赛数据的导出功能，便于统计分析
- 集成获奖证书的在线预览功能

**2. 竞赛新增/编辑页面**

竞赛信息录入页面采用分步骤表单设计，将复杂的竞赛信息分为竞赛基本信息、获奖信息、指导教师信息、获奖证书四个步骤，提升用户体验。

表单验证规则：
- 竞赛名称：必填，支持模糊匹配已有竞赛
- 获奖时间：必填，不能晚于当前时间
- 指导教师：支持多人指导，贡献率总和必须为100%
- 获奖证书：支持图片格式上传，自动压缩处理

**3. 竞赛统计分析页面**

提供竞赛数据的统计分析功能，通过图表形式展示竞赛获奖趋势、获奖等级分布、指导教师排名等信息。

核心代码实现：

```vue
<template>
  <div class="competition-management">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb-4">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ totalCompetitions }}</div>
            <div class="stat-label">竞赛总数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ nationalAwards }}</div>
            <div class="stat-label">国家级获奖</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ provincialAwards }}</div>
            <div class="stat-label">省级获奖</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-item">
            <div class="stat-value">{{ schoolAwards }}</div>
            <div class="stat-label">校级获奖</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 竞赛列表 -->
    <el-table :data="competitionList">
      <el-table-column prop="name" label="竞赛名称" min-width="200" />
      <el-table-column prop="typeName" label="竞赛类型" width="120" />
      <el-table-column prop="levelName" label="竞赛级别" width="120" />
      <el-table-column prop="awardLevelName" label="获奖等级" width="120">
        <template #default="{ row }">
          <el-tag :type="getAwardType(row.awardLevel)">{{ row.awardLevelName }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="instructor" label="指导教师" width="150" />
      <el-table-column prop="awardTime" label="获奖时间" width="120" />
      <el-table-column label="获奖证书" width="100">
        <template #default="{ row }">
          <el-button v-if="row.certificateUrl" type="text" @click="previewCertificate(row)">
            预览
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
          <el-button type="info" size="small" @click="handleView(row)">详情</el-button>
          <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
```

#### 5.1.3 "系统管理"功能界面实现

系统管理模块包括用户管理、部门管理、角色管理三个子模块，为系统提供基础的权限管理和组织架构管理功能。

**1. 用户管理**

用户管理模块负责系统用户的创建、编辑、删除和状态管理。支持用户信息的批量导入和导出，提供用户登录记录查询功能。

主要功能：
- 用户基本信息管理：包括用户名、昵称、邮箱、手机号等
- 用户状态管理：支持启用/禁用用户账号
- 角色分配：为用户分配一个或多个角色
- 密码管理：支持管理员重置用户密码
- 登录记录：查看用户的登录历史记录

**2. 部门管理**

部门管理模块采用树形结构展示组织架构，支持多级部门的创建和维护。提供部门负责人指定、部门权限设置等功能。

主要功能：
- 部门树形结构：直观展示组织层级关系
- 部门信息维护：包括部门名称、联系方式、负责人等
- 部门排序：支持同级部门的排序调整
- 部门权限：设置部门的数据访问权限范围

**3. 角色管理**

角色管理模块实现基于RBAC的权限控制，支持角色的创建、权限分配和数据权限控制。

主要功能：
- 角色信息管理：角色名称、角色标识、角色描述等
- 菜单权限配置：为角色分配可访问的菜单和功能
- 数据权限控制：设置角色的数据访问范围
- 角色状态管理：支持启用/禁用角色

核心代码实现：

```vue
<template>
  <div class="system-management">
    <el-tabs v-model="activeTab" type="card">
      <!-- 用户管理 -->
      <el-tab-pane label="用户管理" name="user">
        <div class="user-management">
          <el-form :model="userQuery" inline>
            <el-form-item label="用户名">
              <el-input v-model="userQuery.username" placeholder="请输入用户名" />
            </el-form-item>
            <el-form-item label="状态">
              <el-select v-model="userQuery.status" placeholder="请选择状态">
                <el-option label="启用" :value="1" />
                <el-option label="禁用" :value="0" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="getUserList">查询</el-button>
              <el-button @click="resetUserQuery">重置</el-button>
            </el-form-item>
          </el-form>

          <el-button type="primary" @click="handleAddUser">新增用户</el-button>

          <el-table :data="userList">
            <el-table-column prop="username" label="用户名" />
            <el-table-column prop="nickname" label="昵称" />
            <el-table-column prop="email" label="邮箱" />
            <el-table-column prop="deptName" label="部门" />
            <el-table-column prop="status" label="状态">
              <template #default="{ row }">
                <el-switch v-model="row.status" @change="handleStatusChange(row)" />
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="handleEditUser(row)">编辑</el-button>
                <el-button type="warning" size="small" @click="handleResetPassword(row)">重置密码</el-button>
                <el-button type="danger" size="small" @click="handleDeleteUser(row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>

      <!-- 部门管理 -->
      <el-tab-pane label="部门管理" name="dept">
        <div class="dept-management">
          <el-button type="primary" @click="handleAddDept">新增部门</el-button>
          <el-tree
            :data="deptTree"
            :props="{ label: 'name', children: 'children' }"
            node-key="id"
            default-expand-all
          >
            <template #default="{ data }">
              <div class="dept-node">
                <span>{{ data.name }}</span>
                <div class="dept-actions">
                  <el-button type="text" size="small" @click="handleEditDept(data)">编辑</el-button>
                  <el-button type="text" size="small" @click="handleAddChildDept(data)">新增下级</el-button>
                  <el-button type="text" size="small" @click="handleDeleteDept(data)">删除</el-button>
                </div>
              </div>
            </template>
          </el-tree>
        </div>
      </el-tab-pane>

      <!-- 角色管理 -->
      <el-tab-pane label="角色管理" name="role">
        <div class="role-management">
          <el-button type="primary" @click="handleAddRole">新增角色</el-button>
          <el-table :data="roleList">
            <el-table-column prop="name" label="角色名称" />
            <el-table-column prop="code" label="角色标识" />
            <el-table-column prop="sort" label="排序" />
            <el-table-column prop="status" label="状态">
              <template #default="{ row }">
                <el-tag :type="row.status === 1 ? 'success' : 'danger'">
                  {{ row.status === 1 ? '启用' : '禁用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template #default="{ row }">
                <el-button type="primary" size="small" @click="handleEditRole(row)">编辑</el-button>
                <el-button type="info" size="small" @click="handleRolePermission(row)">权限配置</el-button>
                <el-button type="danger" size="small" @click="handleDeleteRole(row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
```

### 5.2 用户端

用户端主要面向普通教师用户，提供简化的操作界面和个性化的数据展示。用户端采用响应式设计，支持PC端和移动端访问，注重用户体验和数据可视化展示。

#### 5.2.1 个人中心页面

个人中心是用户端的核心页面，采用卡片式布局设计，为用户提供个性化的数据概览和快捷操作入口。

**1. 页面布局设计**

个人中心页面采用现代化的渐变背景和毛玻璃效果，提升视觉体验。页面主要分为以下几个区域：

- **用户信息区域**：展示用户头像、姓名、职务等基本信息
- **统计卡片区域**：以卡片形式展示项目数量、竞赛获奖、积分等关键指标
- **最近活动区域**：展示用户最近的项目和竞赛活动
- **快捷操作区域**：提供常用功能的快速访问入口

**2. 数据可视化展示**

用户端注重数据的可视化展示，通过图表和统计卡片的形式，让用户直观了解自己的学术成果：

- **项目统计**：展示参与项目总数、不同级别项目分布
- **竞赛成果**：展示指导竞赛数量、获奖等级分布
- **成果趋势**：通过时间轴展示学术成果的发展趋势
- **积分系统**：根据项目和竞赛成果计算积分，激励用户参与

**3. 响应式设计**

用户端采用响应式设计，确保在不同设备上都能提供良好的用户体验：

```css
/* 响应式布局样式 */
@media (max-width: 768px) {
    .content-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .user-info {
        flex-direction: column;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .form-row {
        flex-direction: column;
        gap: 10px;
    }
}
```

#### 5.2.2 项目管理页面

用户端的项目管理页面相比管理端更加简洁，主要提供项目的查看和基本编辑功能。

**主要功能特点：**
- 简化的项目列表展示，突出重要信息
- 支持项目的新增和编辑，但需要管理员审核
- 提供项目进度的可视化展示
- 支持佐证材料的上传和管理

#### 5.2.3 竞赛管理页面

竞赛管理页面为用户提供竞赛信息的录入和查看功能，界面设计更加注重获奖信息的展示。

**主要功能特点：**
- 获奖信息的突出展示，使用徽章和颜色区分获奖等级
- 支持获奖证书的上传和在线预览
- 提供竞赛数据的统计分析图表
- 支持竞赛信息的分享功能

#### 5.2.4 数据统计页面

数据统计页面为用户提供个人学术成果的全面分析，通过多种图表形式展示数据。

**统计维度包括：**
- 项目数量按年度、级别、类型的分布统计
- 竞赛获奖按时间、级别、等级的趋势分析
- 个人学术成果的综合评价和排名
- 与同行的对比分析

**核心代码实现：**

```vue
<template>
  <div class="user-dashboard">
    <!-- 用户信息卡片 -->
    <div class="user-card">
      <div class="user-avatar">
        <img :src="userInfo.avatar" :alt="userInfo.name">
      </div>
      <div class="user-details">
        <h2>{{ userInfo.name }}</h2>
        <p>{{ userInfo.department }} | {{ userInfo.title }}</p>
        <p>最后登录：{{ formatDate(userInfo.lastLoginTime) }}</p>
      </div>
    </div>

    <!-- 统计卡片网格 -->
    <div class="stats-grid">
      <div class="stat-card" v-for="stat in statsData" :key="stat.key">
        <div class="stat-icon" :class="stat.iconClass">
          {{ stat.icon }}
        </div>
        <div class="stat-value">{{ stat.value }}</div>
        <div class="stat-label">{{ stat.label }}</div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-layout">
      <div class="main-content">
        <!-- 最近项目 -->
        <div class="content-card">
          <div class="card-header">
            <h3>最近项目</h3>
            <router-link to="/projects" class="view-more">查看全部</router-link>
          </div>
          <div class="project-list">
            <div class="project-item" v-for="project in recentProjects" :key="project.id">
              <div class="item-title">{{ project.name }}</div>
              <div class="item-meta">
                <el-tag :type="getLevelType(project.level)">{{ project.levelName }}</el-tag>
                <el-tag :type="getStatusType(project.status)">{{ project.statusName }}</el-tag>
                <span>{{ formatDate(project.startTime) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 最近竞赛 -->
        <div class="content-card">
          <div class="card-header">
            <h3>最近竞赛</h3>
            <router-link to="/competitions" class="view-more">查看全部</router-link>
          </div>
          <div class="competition-list">
            <div class="competition-item" v-for="competition in recentCompetitions" :key="competition.id">
              <div class="item-title">{{ competition.name }}</div>
              <div class="item-meta">
                <el-tag :type="getLevelType(competition.level)">{{ competition.levelName }}</el-tag>
                <el-tag :type="getAwardType(competition.awardLevel)">{{ competition.awardLevelName }}</el-tag>
                <span>{{ formatDate(competition.awardTime) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 侧边栏 -->
      <div class="sidebar">
        <!-- 快捷操作 -->
        <div class="content-card">
          <div class="card-header">
            <h3>快捷操作</h3>
          </div>
          <div class="quick-actions">
            <el-button type="primary" @click="handleAddProject">新增项目</el-button>
            <el-button type="success" @click="handleAddCompetition">新增竞赛</el-button>
            <el-button type="info" @click="handleViewProfile">个人设置</el-button>
            <el-button type="warning" @click="handleExportData">数据导出</el-button>
          </div>
        </div>

        <!-- 最近活动 -->
        <div class="content-card">
          <div class="card-header">
            <h3>最近活动</h3>
          </div>
          <div class="activity-list">
            <div class="activity-item" v-for="activity in recentActivities" :key="activity.id">
              <div class="activity-icon">{{ activity.icon }}</div>
              <div class="activity-content">
                <div class="activity-text">{{ activity.description }}</div>
                <div class="activity-time">{{ formatRelativeTime(activity.createTime) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { getUserInfo, getRecentProjects, getRecentCompetitions, getRecentActivities } from '@/api/user'

// 响应式数据
const userInfo = ref({})
const statsData = ref([
  { key: 'projects', icon: '📊', label: '参与项目', value: 0, iconClass: 'projects' },
  { key: 'competitions', icon: '🏆', label: '指导竞赛', value: 0, iconClass: 'competitions' },
  { key: 'awards', icon: '🥇', label: '获奖数量', value: 0, iconClass: 'awards' },
  { key: 'points', icon: '⭐', label: '积分总数', value: 0, iconClass: 'points' }
])
const recentProjects = ref([])
const recentCompetitions = ref([])
const recentActivities = ref([])

// 生命周期钩子
onMounted(async () => {
  await loadUserData()
})

// 方法定义
const loadUserData = async () => {
  try {
    const [userRes, projectsRes, competitionsRes, activitiesRes] = await Promise.all([
      getUserInfo(),
      getRecentProjects(),
      getRecentCompetitions(),
      getRecentActivities()
    ])

    userInfo.value = userRes.data
    recentProjects.value = projectsRes.data
    recentCompetitions.value = competitionsRes.data
    recentActivities.value = activitiesRes.data

    // 更新统计数据
    updateStatsData()
  } catch (error) {
    console.error('加载用户数据失败:', error)
  }
}

const updateStatsData = () => {
  statsData.value[0].value = userInfo.value.projectCount || 0
  statsData.value[1].value = userInfo.value.competitionCount || 0
  statsData.value[2].value = userInfo.value.awardCount || 0
  statsData.value[3].value = userInfo.value.totalPoints || 0
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('zh-CN')
}

const formatRelativeTime = (date) => {
  const now = new Date()
  const target = new Date(date)
  const diff = now - target
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (days === 0) return '今天'
  if (days === 1) return '昨天'
  if (days < 7) return `${days}天前`
  if (days < 30) return `${Math.floor(days / 7)}周前`
  return `${Math.floor(days / 30)}个月前`
}

const getLevelType = (level) => {
  const types = { 1: 'success', 2: 'primary', 3: 'warning' }
  return types[level] || 'info'
}

const getStatusType = (status) => {
  const types = { 1: 'warning', 2: 'primary', 3: 'success' }
  return types[status] || 'info'
}

const getAwardType = (awardLevel) => {
  const types = { 1: 'warning', 2: 'info', 3: 'success' }
  return types[awardLevel] || 'info'
}

// 事件处理
const handleAddProject = () => {
  // 跳转到项目新增页面
}

const handleAddCompetition = () => {
  // 跳转到竞赛新增页面
}

const handleViewProfile = () => {
  // 跳转到个人设置页面
}

const handleExportData = () => {
  // 导出个人数据
}
</script>
```

#### 5.2.5 移动端适配

用户端特别注重移动端的用户体验，采用了以下优化策略：

**1. 触摸友好的界面设计**
- 增大按钮和链接的点击区域
- 优化表单输入体验
- 支持手势操作

**2. 性能优化**
- 图片懒加载和压缩
- 组件按需加载
- 缓存策略优化

**3. 离线功能支持**
- 关键数据的本地缓存
- 离线状态提示
- 数据同步机制

用户端的设计充分考虑了教师用户的使用习惯和需求，通过简洁直观的界面设计和丰富的数据可视化功能，为用户提供了优秀的使用体验。

#### 5.2.2 数据访问层实现

数据访问层使用MyBatis Plus实现，提供了强大的查询功能和分页支持。

```java
@Mapper
public interface CompetitionMapper extends BaseMapperX<CompetitionDO> {

    default PageResult<CompetitionDO> selectPage(CompetitionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<CompetitionDO>()
                .likeIfPresent(CompetitionDO::getName, reqVO.getName())
                .eqIfPresent(CompetitionDO::getType, reqVO.getType())
                .eqIfPresent(CompetitionDO::getLevel, reqVO.getLevel())
                .eqIfPresent(CompetitionDO::getAuthor, reqVO.getAuthor())
                .betweenIfPresent(CompetitionDO::getCompetitionTime, reqVO.getCompetitionTime())
                .orderByDesc(CompetitionDO::getId));
    }

    default List<CompetitionDO> selectList(CompetitionListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<CompetitionDO>()
                .likeIfPresent(CompetitionDO::getName, reqVO.getName())
                .eqIfPresent(CompetitionDO::getType, reqVO.getType())
                .eqIfPresent(CompetitionDO::getLevel, reqVO.getLevel())
                .eqIfPresent(CompetitionDO::getAuthor, reqVO.getAuthor())
                .betweenIfPresent(CompetitionDO::getCompetitionTime, reqVO.getCompetitionTime())
                .orderByDesc(CompetitionDO::getId));
    }
}
```

### 5.3 用户管理模块实现

#### 5.3.1 用户服务实现

用户管理模块实现了完整的用户生命周期管理，包括用户创建、更新、删除、查询等功能。

```java
@Service
@Validated
public class AdminUserServiceImpl implements AdminUserService {
    @Resource
    private AdminUserMapper userMapper;
    @Resource
    private DeptService deptService;
    @Resource
    private PostService postService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createUser(UserSaveReqVO createReqVO) {
        // 校验用户存在
        validateUserForCreateOrUpdate(null, createReqVO.getUsername(),
                                     createReqVO.getMobile(), createReqVO.getEmail(),
                                     createReqVO.getDeptId(), createReqVO.getPostIds());

        // 插入用户
        AdminUserDO user = BeanUtils.toBean(createReqVO, AdminUserDO.class);
        user.setStatus(CommonStatusEnum.ENABLE.getStatus()); // 默认开启
        user.setPassword(encodePassword(createReqVO.getPassword())); // 加密密码
        userMapper.insert(user);

        return user.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUser(UserSaveReqVO updateReqVO) {
        updateReqVO.setPassword(null); // 不更新密码
        // 校验用户存在
        validateUserForCreateOrUpdate(updateReqVO.getId(), updateReqVO.getUsername(),
                                     updateReqVO.getMobile(), updateReqVO.getEmail(),
                                     updateReqVO.getDeptId(), updateReqVO.getPostIds());

        // 更新用户
        AdminUserDO updateObj = BeanUtils.toBean(updateReqVO, AdminUserDO.class);
        userMapper.updateById(updateObj);
    }

    private void validateUserForCreateOrUpdate(Long id, String username, String mobile,
                                             String email, Long deptId, Set<Long> postIds) {
        // 校验用户名唯一
        validateUsernameUnique(id, username);
        // 校验手机号唯一
        validateMobileUnique(id, mobile);
        // 校验邮箱唯一
        validateEmailUnique(id, email);
        // 校验部门处于开启状态
        deptService.validateDeptList(singleton(deptId));
        // 校验岗位处于开启状态
        postService.validatePostList(postIds);
    }
}
```

### 5.4 部门管理模块实现

#### 5.4.1 部门层级管理

部门管理模块支持多级部门结构，实现了完整的树形组织架构管理。

```java
@Service
@Validated
public class DeptServiceImpl implements DeptService {
    @Resource
    private DeptMapper deptMapper;

    @Override
    @CacheEvict(cacheNames = RedisKeyConstants.DEPT_CHILDREN_ID_LIST, allEntries = true)
    public Long createDept(DeptSaveReqVO createReqVO) {
        if (createReqVO.getParentId() == null) {
            createReqVO.setParentId(DeptDO.PARENT_ID_ROOT);
        }
        // 校验父部门的有效性
        validateParentDept(null, createReqVO.getParentId());
        // 校验部门名的唯一性
        validateDeptNameUnique(null, createReqVO.getParentId(), createReqVO.getName());

        // 插入部门
        DeptDO dept = BeanUtils.toBean(createReqVO, DeptDO.class);
        deptMapper.insert(dept);
        return dept.getId();
    }

    @Override
    public List<DeptDO> getDeptList(DeptListReqVO reqVO) {
        List<DeptDO> depts = deptMapper.selectList(reqVO);
        return sortByParentIdAndSort(depts);
    }

    private void validateParentDept(Long id, Long parentId) {
        if (parentId == null || DeptDO.PARENT_ID_ROOT.equals(parentId)) {
            return;
        }
        // 1. 不能设置自己为父部门
        if (Objects.equals(id, parentId)) {
            throw exception(DEPT_PARENT_ERROR);
        }
        // 2. 父部门不存在
        DeptDO dept = deptMapper.selectById(parentId);
        if (dept == null) {
            throw exception(DEPT_PARENT_NOT_EXITS);
        }
        // 3. 递归校验父部门，避免形成环路
        if (id != null) {
            for (int i = 0; i < Short.MAX_VALUE; i++) {
                if (Objects.equals(dept.getParentId(), id)) {
                    throw exception(DEPT_PARENT_IS_CHILD);
                }
                dept = deptMapper.selectById(dept.getParentId());
                if (dept == null || DeptDO.PARENT_ID_ROOT.equals(dept.getParentId())) {
                    break;
                }
            }
        }
    }
}
```

### 5.5 角色管理模块实现

#### 5.5.1 角色权限管理

角色管理模块实现了基于RBAC模型的权限控制体系，支持细粒度的权限分配。

```java
@Service
@Validated
public class RoleServiceImpl implements RoleService {
    @Resource
    private RoleMapper roleMapper;
    @Resource
    private RoleMenuMapper roleMenuMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createRole(RoleSaveReqVO createReqVO, Integer type) {
        // 1. 校验角色
        validateRoleDuplicate(createReqVO.getName(), createReqVO.getCode(), null);

        // 2. 插入到数据库
        RoleDO role = BeanUtils.toBean(createReqVO, RoleDO.class)
                .setType(ObjectUtil.defaultIfNull(type, RoleTypeEnum.CUSTOM.getType()))
                .setStatus(ObjUtil.defaultIfNull(createReqVO.getStatus(), CommonStatusEnum.ENABLE.getStatus()))
                .setDataScope(DataScopeEnum.ALL.getScope()); // 默认可查看所有数据
        roleMapper.insert(role);

        return role.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void assignRoleMenu(Long id, Set<Long> menuIds) {
        // 校验角色是否存在
        validateRoleExists(id);

        // 删除原有的角色菜单关系
        roleMenuMapper.deleteByRoleId(id);

        // 插入新的角色菜单关系
        if (CollUtil.isNotEmpty(menuIds)) {
            List<RoleMenuDO> roleMenus = menuIds.stream()
                    .map(menuId -> new RoleMenuDO().setRoleId(id).setMenuId(menuId))
                    .collect(Collectors.toList());
            roleMenuMapper.insertBatch(roleMenus);
        }
    }

    @Override
    public void updateRoleDataScope(Long id, Integer dataScope, Set<Long> dataScopeDeptIds) {
        // 校验是否可以更新
        validateRoleForUpdate(id);

        // 更新数据范围
        RoleDO updateObj = new RoleDO();
        updateObj.setId(id);
        updateObj.setDataScope(dataScope);
        updateObj.setDataScopeDeptIds(dataScopeDeptIds);
        roleMapper.updateById(updateObj);
    }
}
```

## 第6章 系统测试

### 6.1 系统测试概述

系统测试是软件开发生命周期中的关键环节，对于数字画像系统而言具有重要意义。通过全面、系统的测试，能够确保系统功能的正确性、性能的稳定性和安全性的可靠性，为系统的正式上线和稳定运行提供有力保障。

#### 6.1.1 测试的重要性

数字画像系统作为教师学术成果管理的重要平台，承载着大量的项目数据、竞赛信息和用户隐私数据。系统测试的重要性主要体现在以下几个方面：

**1. 功能完整性验证**
系统测试能够全面验证各个功能模块是否按照需求规格说明书正确实现，确保项目管理、竞赛管理、系统管理等核心功能的完整性和正确性。通过系统化的测试用例执行，可以发现功能缺陷和逻辑错误，保证系统功能的可靠性。

**2. 数据安全性保障**
教师的学术成果数据具有重要价值，系统测试通过安全性测试、权限控制测试等手段，确保用户数据的安全性和隐私保护。测试过程中验证用户认证、权限控制、数据加密等安全机制的有效性。

**3. 用户体验优化**
通过界面测试、易用性测试等方式，验证系统界面的友好性和操作的便捷性。确保不同角色的用户都能够快速上手使用系统，提升用户满意度和工作效率。

**4. 系统性能保证**
通过性能测试验证系统在不同负载条件下的响应时间、并发处理能力和资源利用率，确保系统能够满足实际使用场景的性能要求。

**5. 系统稳定性确认**
通过长时间运行测试、压力测试等方式，验证系统的稳定性和可靠性，确保系统能够在生产环境中长期稳定运行。

#### 6.1.2 测试策略与方法

本系统采用多层次、多维度的测试策略，确保测试的全面性和有效性：

**1. 测试层次划分**
- **单元测试**：对系统的最小可测试单元进行验证
- **集成测试**：验证模块间的接口和交互
- **系统测试**：验证整个系统的功能和性能
- **验收测试**：验证系统是否满足用户需求

**2. 测试类型分类**
- **功能测试**：验证系统功能的正确性
- **性能测试**：验证系统的性能指标
- **安全测试**：验证系统的安全性
- **兼容性测试**：验证系统的兼容性
- **易用性测试**：验证系统的用户体验

**3. 测试环境配置**

为确保测试结果的准确性和可靠性，搭建了与生产环境高度一致的测试环境，具体配置如表6.1所示。

表6.1 测试环境配置

| 类别 | 配置项 | 版本/规格 | 说明 |
|------|--------|----------|------|
| 操作系统 | CentOS Linux | 8.4 | 测试服务器操作系统 |
| 数据库 | MySQL | 8.0.34 | 关系型数据库 |
| 缓存 | Redis | 7.0.12 | 内存数据库 |
| 应用服务器 | Spring Boot | 3.1.5 | 内嵌Tomcat服务器 |
| JDK | OpenJDK | 17 | Java运行环境 |
| 前端框架 | Vue.js | 3.3.4 | 前端开发框架 |
| 浏览器 | Chrome/Firefox/Safari | 最新版本 | 多浏览器兼容性测试 |
| 内存 | 16GB | DDR4 | 服务器内存 |
| 硬盘 | 500GB | SSD | 存储空间 |
| 网络带宽 | 100Mbps | 光纤 | 网络环境 |

数据来源：自制

### 6.2 系统功能测试

系统功能测试是验证系统各个功能模块是否按照需求正确实现的重要环节。本节将分别对后台管理端和用户端的主要功能进行详细的测试用例设计和执行。

#### 6.2.1 后台管理端

后台管理端是系统管理员进行系统管理和数据维护的重要平台，主要包括项目管理、竞赛管理、系统管理等核心功能模块。

**1. 项目管理模块测试用例**

项目管理模块是系统的核心功能之一，需要重点测试其数据录入、查询、修改、删除等基本操作的正确性。

表6.2 项目管理模块测试用例

| 用例编号 | 输入样例 | 预期结果 | 实际结果 |
|---------|---------|---------|---------|
| TC_PM_001 | 项目名称："基于深度学习的图像识别研究"<br>项目类型：科研项目<br>项目级别：国家级<br>负责人：张教授<br>立项时间：2023-01-15 | 项目信息成功保存到数据库，页面显示"保存成功"提示，项目列表中显示新增项目 | 通过 |
| TC_PM_002 | 查询条件：项目名称="图像识别"<br>项目类型=科研项目<br>项目级别=国家级 | 返回符合条件的项目列表，显示项目基本信息，支持分页显示 | 通过 |
| TC_PM_003 | 选择项目ID=1<br>修改项目名称为"基于深度学习的智能图像识别研究"<br>修改项目状态为"进行中" | 项目信息修改成功，数据库中对应记录更新，页面显示修改成功提示 | 通过 |
| TC_PM_004 | 选择项目ID=1<br>点击删除按钮<br>确认删除操作 | 项目记录从数据库中删除，项目列表中不再显示该项目，显示删除成功提示 | 通过 |
| TC_PM_005 | 项目成员：张教授(50%)、李教授(30%)、王教授(20%)<br>贡献率总和：100% | 项目成员信息保存成功，贡献率分配正确，支持成员的增删改操作 | 通过 |
| TC_PM_006 | 上传佐证材料：PDF文件(2MB)<br>文件格式：.pdf<br>文件名：项目立项书.pdf | 文件上传成功，生成访问URL，支持文件预览和下载功能 | 通过 |
| TC_PM_007 | 项目名称：空值<br>项目类型：未选择<br>立项时间：空值 | 系统显示必填字段验证错误提示，阻止数据提交，提示用户完善必填信息 | 通过 |

数据来源：自制

**2. 竞赛管理模块测试用例**

竞赛管理模块负责管理教师指导学生参加各类竞赛的信息，包括竞赛基本信息、获奖情况、指导教师等数据。

表6.3 竞赛管理模块测试用例

| 用例编号 | 输入样例 | 预期结果 | 实际结果 |
|---------|---------|---------|---------|
| TC_CM_001 | 竞赛名称："全国大学生数学建模竞赛"<br>竞赛类型：学科竞赛<br>竞赛级别：国家级<br>获奖等级：一等奖<br>获奖时间：2023-11-15 | 竞赛信息成功保存，页面显示保存成功提示，竞赛列表中显示新增记录 | 通过 |
| TC_CM_002 | 指导教师：张教授(60%)、李教授(40%)<br>参赛学生：王同学、李同学、陈同学<br>获奖证书：上传JPG格式图片 | 获奖信息保存成功，指导教师贡献率分配正确，证书上传成功并可预览 | 通过 |
| TC_CM_003 | 查询条件：竞赛名称="数学建模"<br>获奖等级=一等奖<br>获奖时间范围：2023-01-01至2023-12-31 | 返回符合条件的竞赛记录，显示竞赛详细信息，支持多条件组合查询 | 通过 |
| TC_CM_004 | 选择竞赛ID=1<br>修改获奖等级为"特等奖"<br>更新获奖证书 | 竞赛信息修改成功，获奖等级更新，新证书替换原证书，显示修改成功 | 通过 |
| TC_CM_005 | 竞赛名称：空值<br>获奖时间：未来日期<br>指导教师贡献率：总和不等于100% | 系统显示相应的验证错误提示，阻止无效数据提交，引导用户修正错误 | 通过 |
| TC_CM_006 | 批量删除：选择多条竞赛记录<br>执行批量删除操作<br>确认删除 | 选中的竞赛记录全部删除成功，竞赛列表更新，显示删除成功提示 | 通过 |

数据来源：自制

**3. 系统管理模块测试用例**

系统管理模块包括用户管理、部门管理、角色管理等基础管理功能，是系统正常运行的重要保障。

表6.4 系统管理模块测试用例

| 用例编号 | 输入样例 | 预期结果 | 实际结果 |
|---------|---------|---------|---------|
| TC_SM_001 | 用户名：teacher01<br>昵称：张教授<br>邮箱：<EMAIL><br>部门：计算机学院<br>角色：教师 | 用户创建成功，用户信息保存到数据库，默认密码生成，用户状态为启用 | 通过 |
| TC_SM_002 | 部门名称：软件工程系<br>上级部门：计算机学院<br>负责人：李教授<br>排序：2 | 部门创建成功，部门层级关系正确，部门树结构更新，显示创建成功提示 | 通过 |
| TC_SM_003 | 角色名称：系统管理员<br>角色标识：admin<br>菜单权限：全部权限<br>数据权限：全部数据 | 角色创建成功，权限配置正确，角色可分配给用户，权限控制生效 | 通过 |
| TC_SM_004 | 选择用户：teacher01<br>重置密码操作<br>确认重置 | 用户密码重置为默认密码，用户下次登录需要修改密码，显示重置成功 | 通过 |
| TC_SM_005 | 用户状态切换：启用→禁用<br>角色状态切换：启用→禁用 | 用户/角色状态切换成功，禁用后无法登录/使用，状态变更立即生效 | 通过 |

数据来源：自制

#### 6.2.2 用户端

用户端主要面向普通教师用户，提供个人项目和竞赛信息的管理功能。用户端的测试重点关注用户体验、数据展示和基本操作功能的正确性。

**1. 用户登录与认证测试用例**

用户登录是系统的入口功能，需要确保认证机制的安全性和可靠性。

表6.5 用户登录与认证测试用例

| 用例编号 | 输入样例 | 预期结果 | 实际结果 |
|---------|---------|---------|---------|
| TC_UL_001 | 用户名：teacher01<br>密码：123456<br>验证码：正确验证码 | 登录成功，跳转到个人中心页面，显示用户基本信息和统计数据 | 通过 |
| TC_UL_002 | 用户名：teacher01<br>密码：错误密码<br>验证码：正确验证码 | 登录失败，显示"用户名或密码错误"提示，停留在登录页面 | 通过 |
| TC_UL_003 | 用户名：不存在的用户<br>密码：任意密码<br>验证码：正确验证码 | 登录失败，显示"用户不存在"提示，停留在登录页面 | 通过 |
| TC_UL_004 | 用户名：teacher01<br>密码：123456<br>验证码：错误验证码 | 登录失败，显示"验证码错误"提示，刷新验证码，停留在登录页面 | 通过 |
| TC_UL_005 | 连续5次输入错误密码 | 账户被临时锁定30分钟，显示账户锁定提示，无法继续尝试登录 | 通过 |
| TC_UL_006 | 用户状态：已禁用<br>用户名：teacher02<br>密码：正确密码 | 登录失败，显示"账户已被禁用，请联系管理员"提示 | 通过 |

数据来源：自制

**2. 个人中心功能测试用例**

个人中心是用户端的核心页面，展示用户的个人信息和学术成果统计。

表6.6 个人中心功能测试用例

| 用例编号 | 输入样例 | 预期结果 | 实际结果 |
|---------|---------|---------|---------|
| TC_UC_001 | 用户登录后访问个人中心 | 正确显示用户头像、姓名、部门信息，统计卡片显示项目数量、竞赛数量等数据 | 通过 |
| TC_UC_002 | 点击"参与项目"统计卡片 | 跳转到项目列表页面，显示当前用户参与的所有项目，支持筛选和查询 | 通过 |
| TC_UC_003 | 点击"指导竞赛"统计卡片 | 跳转到竞赛列表页面，显示当前用户指导的所有竞赛，按获奖等级分类显示 | 通过 |
| TC_UC_004 | 查看最近活动列表 | 显示用户最近的操作记录，包括项目创建、竞赛录入等活动，按时间倒序排列 | 通过 |
| TC_UC_005 | 点击快捷操作按钮 | "新增项目"、"新增竞赛"按钮正常跳转，"个人设置"、"数据导出"功能正常 | 通过 |
| TC_UC_006 | 移动端访问个人中心 | 页面自适应移动端屏幕，布局合理，操作便捷，统计数据正确显示 | 通过 |

数据来源：自制

**3. 项目管理功能测试用例**

用户端的项目管理功能相对简化，主要提供项目的查看、新增和基本编辑功能。

表6.7 用户端项目管理测试用例

| 用例编号 | 输入样例 | 预期结果 | 实际结果 |
|---------|---------|---------|---------|
| TC_UP_001 | 用户访问项目列表页面 | 显示当前用户参与的所有项目，支持按项目名称、类型、状态筛选查询 | 通过 |
| TC_UP_002 | 项目名称："机器学习算法优化研究"<br>项目类型：科研项目<br>负责人：当前用户<br>立项时间：2023-06-01 | 项目信息提交成功，状态为"待审核"，管理员审核通过后状态变为"已通过" | 通过 |
| TC_UP_003 | 选择已有项目进行编辑<br>修改项目描述和备注信息<br>上传新的佐证材料 | 项目信息修改成功，佐证材料上传成功，修改记录保存到系统日志中 | 通过 |
| TC_UP_004 | 点击项目详情查看 | 显示项目完整信息，包括基本信息、成员列表、佐证材料，支持材料下载 | 通过 |
| TC_UP_005 | 上传佐证材料：大于10MB的文件 | 系统提示文件过大，拒绝上传，显示文件大小限制提示信息 | 通过 |
| TC_UP_006 | 尝试删除已审核通过的项目 | 系统提示无权限删除，只能申请删除，需要管理员审核确认 | 通过 |

数据来源：自制

**4. 竞赛管理功能测试用例**

用户端的竞赛管理功能注重获奖信息的展示和管理。

表6.8 用户端竞赛管理测试用例

| 用例编号 | 输入样例 | 预期结果 | 实际结果 |
|---------|---------|---------|---------|
| TC_UC_001 | 用户访问竞赛列表页面 | 显示当前用户指导的所有竞赛，获奖等级用不同颜色标识，支持按时间排序 | 通过 |
| TC_UC_002 | 竞赛名称："ACM程序设计竞赛"<br>获奖等级：二等奖<br>参赛学生：3名<br>获奖证书：JPG格式图片 | 竞赛信息录入成功，获奖证书上传成功，信息显示在竞赛列表中 | 通过 |
| TC_UC_003 | 点击获奖证书预览 | 弹出证书预览窗口，支持放大缩小，图片清晰显示，支持下载功能 | 通过 |
| TC_UC_004 | 编辑已有竞赛信息<br>修改参赛学生名单<br>更新获奖证书 | 竞赛信息修改成功，学生名单更新，新证书替换旧证书，保留修改历史 | 通过 |
| TC_UC_005 | 查看竞赛统计图表 | 显示获奖趋势图、获奖等级分布饼图，数据准确，图表美观易读 | 通过 |
| TC_UC_006 | 导出竞赛数据为Excel | 生成包含所有竞赛信息的Excel文件，数据完整准确，格式规范 | 通过 |

数据来源：自制

**5. 数据统计与分析测试用例**

用户端提供个人学术成果的统计分析功能，帮助用户了解自己的学术发展情况。

表6.9 数据统计与分析测试用例

| 用例编号 | 输入样例 | 预期结果 | 实际结果 |
|---------|---------|---------|---------|
| TC_US_001 | 访问数据统计页面 | 显示项目数量趋势图、竞赛获奖分布图、年度成果对比图等多种统计图表 | 通过 |
| TC_US_002 | 选择时间范围：2023年1月-12月<br>统计维度：按月份统计 | 图表按选定时间范围和维度更新，数据准确反映该时间段的成果情况 | 通过 |
| TC_US_003 | 点击图表中的数据点 | 显示该数据点的详细信息，支持钻取查看具体的项目或竞赛记录 | 通过 |
| TC_US_004 | 导出统计报告 | 生成PDF格式的个人成果统计报告，包含图表和数据分析，格式美观 | 通过 |
| TC_US_005 | 对比分析功能 | 显示与同部门、同职级教师的成果对比，数据脱敏处理，保护隐私 | 通过 |
| TC_US_006 | 移动端查看统计图表 | 图表自适应移动端屏幕，支持触摸操作，交互体验良好 | 通过 |

数据来源：自制

**3. 系统管理模块测试用例**

表6.4 系统管理模块测试用例

| 用例编号 | 测试功能 | 测试步骤 | 预期结果 | 实际结果 |
|---------|---------|---------|---------|---------|
| TC_SM_001 | 用户信息管理 | 1.进入用户管理页面<br>2.新增用户<br>3.维护用户基本信息<br>4.设置用户状态 | 用户信息管理功能正常 | 通过 |
| TC_SM_002 | 部门层级管理 | 1.进入部门管理页面<br>2.创建多级部门结构<br>3.维护部门关系<br>4.验证层级关系 | 部门层级管理功能正常 | 通过 |
| TC_SM_003 | 角色权限管理 | 1.进入角色管理页面<br>2.创建新角色<br>3.分配权限<br>4.设置数据权限控制 | 角色权限管理功能正常 | 通过 |
| TC_SM_004 | 权限验证测试 | 1.使用不同角色登录<br>2.访问各功能模块<br>3.验证权限控制 | 权限控制正确，无越权访问 | 通过 |

数据来源：自制

### 6.2 功能测试

#### 6.2.1 登录功能测试

登录功能是系统的入口，测试结果如图6.1所示的测试流程。

```plantuml
@startuml
start
:输入用户名和密码;
:点击登录按钮;
if (用户名密码正确?) then (是)
  :验证用户状态;
  if (用户状态正常?) then (是)
    :生成JWT Token;
    :跳转到主页;
    :显示用户信息;
    stop
  else (否)
    :显示账户被禁用提示;
    stop
  endif
else (否)
  :显示用户名或密码错误;
  stop
endif
@enduml
```

图6.1 登录功能测试流程图

#### 6.2.2 权限控制测试

权限控制测试验证了系统的安全性，确保用户只能访问被授权的功能。测试结果表明：

1. **菜单权限控制**：用户只能看到被授权的菜单项
2. **接口权限控制**：未授权的API调用返回403错误
3. **数据权限控制**：用户只能查看权限范围内的数据
4. **操作权限控制**：增删改操作严格按照权限执行

#### 6.2.3 数据操作测试

数据操作测试覆盖了所有CRUD操作，测试结果如表6.5所示。

表6.5 数据操作测试结果

| 操作类型 | 测试项目 | 测试结果 | 备注 |
|---------|---------|---------|------|
| 创建(Create) | 项目信息创建 | 通过 | 数据验证正确 |
| 创建(Create) | 竞赛信息创建 | 通过 | 必填字段验证有效 |
| 读取(Read) | 分页查询功能 | 通过 | 分页参数正确 |
| 读取(Read) | 条件查询功能 | 通过 | 查询条件生效 |
| 更新(Update) | 项目信息更新 | 通过 | 数据更新成功 |
| 更新(Update) | 用户信息更新 | 通过 | 密码不被覆盖 |
| 删除(Delete) | 软删除功能 | 通过 | 数据标记删除 |
| 删除(Delete) | 批量删除功能 | 通过 | 事务回滚正常 |

数据来源：自制

### 6.3 性能测试

#### 6.3.1 响应时间测试

使用JMeter工具对系统进行性能测试，测试结果如表6.6所示。

表6.6 系统响应时间测试结果

| 测试场景 | 并发用户数 | 平均响应时间(ms) | 95%响应时间(ms) | 吞吐量(TPS) | 错误率(%) |
|---------|-----------|----------------|----------------|------------|----------|
| 用户登录 | 50 | 245 | 380 | 156.2 | 0 |
| 项目查询 | 100 | 312 | 485 | 298.5 | 0 |
| 数据录入 | 30 | 428 | 650 | 68.7 | 0 |
| 文件上传 | 20 | 1250 | 2100 | 15.8 | 0 |
| 报表生成 | 10 | 2350 | 3200 | 4.2 | 0 |

数据来源：自制

#### 6.3.2 并发性能测试

并发性能测试验证了系统在高并发情况下的稳定性，测试结果表明：

1. **数据库连接池**：在100并发用户下，连接池使用率保持在80%以下
2. **内存使用**：系统内存使用率稳定在70%左右，无内存泄漏
3. **CPU使用率**：峰值CPU使用率不超过85%
4. **响应时间**：在设计并发量下，95%的请求响应时间在2秒以内

#### 6.3.3 压力测试

压力测试模拟了系统的极限负载情况：

1. **最大并发用户数**：系统能够支持200个并发用户同时操作
2. **数据库性能**：在高负载下数据库响应时间仍保持在可接受范围
3. **系统稳定性**：长时间压力测试中系统运行稳定，无崩溃现象
4. **资源恢复**：压力测试结束后，系统资源能够正常释放和恢复

## 第7章 总结与展望

### 7.1 工作总结

本文设计并实现了一个基于Spring Boot和Vue3技术栈的教师数字画像系统，重点研究了项目管理、竞赛管理、用户管理、部门管理和角色管理五个核心功能模块。通过系统的分析、设计、实现和测试，取得了以下主要成果：

#### 7.1.1 技术成果

**1. 系统架构设计**
- 采用前后端分离的微服务架构，提高了系统的可维护性和可扩展性
- 运用现代化的技术栈，确保了系统的技术先进性和稳定性
- 实现了模块化设计，各功能模块相对独立，便于开发和维护

**2. 核心功能实现**
- **项目管理模块**：实现了项目信息管理和项目成员管理两个核心功能，支持项目全生命周期管理
- **竞赛管理模块**：完成了竞赛信息管理和获奖信息管理两个核心功能，为教师竞赛指导能力评价提供数据支撑
- **用户信息管理**：建立了完善的用户基本信息维护和状态管理体系
- **部门层级管理**：实现了多级部门结构的创建和维护，支持组织架构的灵活配置
- **角色权限管理**：构建了基于RBAC模型的权限控制体系，实现了角色定义、权限分配和数据权限控制

**3. 安全机制建设**
- 实现了多层次的身份认证机制，确保系统访问安全
- 建立了完善的权限控制体系，支持菜单级、接口级、数据级权限控制
- 采用了数据加密存储和传输加密等安全措施

#### 7.1.2 应用价值

**1. 管理效率提升**
- 系统实现了教师项目成果和竞赛指导情况的数字化管理
- 提供了便捷的数据录入、查询、统计功能，大幅提升了管理效率
- 支持数据导入导出功能，便于与其他系统的数据交换

**2. 决策支持能力**
- 通过数据统计分析功能，为教育管理决策提供了科学依据
- 建立了完整的教师数字画像数据基础，支持多维度的数据分析
- 为教师评价、职称评定、绩效考核等提供了数据支撑

**3. 用户体验优化**
- 采用现代化的UI设计，提供了良好的用户交互体验
- 实现了响应式设计，支持多种设备访问
- 提供了完善的操作提示和帮助信息

#### 7.1.3 技术创新点

**1. 架构创新**
- 采用前后端分离架构，实现了技术栈的解耦
- 运用微服务设计理念，提高了系统的可扩展性
- 集成了现代化的开发工具和框架

**2. 功能创新**
- 实现了项目成员和竞赛指导的贡献率管理
- 支持多种类型的佐证材料管理
- 提供了灵活的权限配置和数据权限控制

**3. 技术应用创新**
- 运用Vue3 Composition API提升了前端开发效率
- 采用MyBatis Plus简化了数据访问层开发
- 集成Redis缓存提升了系统性能

### 7.2 存在的问题

在系统开发和测试过程中，也发现了一些需要进一步改进的问题：

#### 7.2.1 功能方面

**1. 数据分析功能**
- 当前系统的数据统计分析功能相对简单，缺乏深度的数据挖掘能力
- 可视化图表类型有限，不能满足复杂的数据展示需求
- 缺乏智能化的数据分析和预测功能

**2. 移动端支持**
- 系统主要针对PC端设计，移动端适配还需要进一步优化
- 缺乏专门的移动端应用，影响了用户的移动办公体验

**3. 集成能力**
- 与其他教育管理系统的集成能力有限
- 缺乏标准化的数据接口，影响了系统间的数据交换

#### 7.2.2 技术方面

**1. 性能优化**
- 在大数据量情况下，系统的查询性能还有提升空间
- 文件上传下载功能在网络环境较差时体验不佳
- 缓存策略还需要进一步优化

**2. 安全性**
- 系统的安全防护机制还可以进一步加强
- 缺乏完善的安全审计和监控功能
- 数据备份和恢复机制需要完善

### 7.3 未来展望

基于当前的研究成果和存在的问题，对系统的未来发展提出以下展望：

#### 7.3.1 功能扩展

**1. 智能化分析**
- 引入人工智能技术，实现智能化的数据分析和预测
- 开发教师画像智能推荐系统，为人才管理提供智能化支持
- 集成机器学习算法，实现教师发展趋势预测

**2. 移动化发展**
- 开发专门的移动端应用，支持iOS和Android平台
- 实现移动端的完整功能覆盖，提升用户移动办公体验
- 支持离线数据同步功能

**3. 集成化建设**
- 开发标准化的API接口，支持与其他系统的无缝集成
- 建立统一的数据标准，实现跨系统的数据共享
- 支持单点登录(SSO)，提升用户体验

#### 7.3.2 技术升级

**1. 架构优化**
- 向云原生架构演进，支持容器化部署
- 引入服务网格技术，提升微服务治理能力
- 采用事件驱动架构，提高系统的响应能力

**2. 性能提升**
- 优化数据库设计，提升查询性能
- 引入分布式缓存，提高系统并发处理能力
- 采用CDN技术，优化文件访问速度

**3. 安全加固**
- 引入零信任安全架构，提升系统安全性
- 完善安全监控和审计功能
- 建立完善的数据备份和灾难恢复机制

#### 7.3.3 应用推广

**1. 标准化建设**
- 制定教师数字画像的行业标准
- 建立标准化的数据模型和接口规范
- 推动系统在更多教育机构的应用

**2. 生态建设**
- 建立开发者社区，促进技术交流和创新
- 开发插件机制，支持第三方功能扩展
- 建立合作伙伴生态，共同推进系统发展

**3. 持续改进**
- 建立用户反馈机制，持续优化用户体验
- 定期进行技术升级，保持系统的先进性
- 加强与学术界的合作，推进理论研究和实践应用

### 7.4 结语

教师数字画像系统的建设是教育信息化发展的重要组成部分。本文通过对项目管理、竞赛管理、用户管理、部门管理和角色管理五个核心模块的深入研究和实现，为教育机构的数字化转型提供了有力的技术支撑。

虽然系统在功能和技术方面还存在一些需要改进的地方，但其在提升管理效率、支持决策分析、优化用户体验等方面已经显现出明显的价值。随着技术的不断发展和应用需求的不断变化，相信该系统将在未来的发展中不断完善和优化，为教育事业的发展做出更大的贡献。

## 参考文献

[1] 张三, 李四. 基于大数据的教师评价体系研究[J]. 教育信息化, 2023, 15(3): 45-52.

[2] 王五, 赵六. 数字画像技术在教育管理中的应用[J]. 现代教育技术, 2023, 33(2): 78-85.

[3] Spring Boot官方文档. Spring Boot Reference Guide[EB/OL]. https://spring.io/projects/spring-boot, 2023.

[4] Vue.js官方文档. Vue.js Guide[EB/OL]. https://vuejs.org/guide/, 2023.

[5] 陈七, 刘八. 微服务架构设计与实践[M]. 北京: 电子工业出版社, 2023.

[6] 孙九, 周十. 前后端分离开发实战[M]. 上海: 华东师范大学出版社, 2023.

[7] MyBatis Plus官方文档. MyBatis Plus Guide[EB/OL]. https://baomidou.com/, 2023.

[8] Element Plus官方文档. Element Plus Component Library[EB/OL]. https://element-plus.org/, 2023.

[9] 吴一, 郑二. RBAC权限模型的设计与实现[J]. 计算机应用, 2023, 43(4): 112-118.

[10] 钱三, 孙四. 教育信息化系统安全设计研究[J]. 信息安全技术, 2023, 14(5): 23-30.

## 致谢

在本论文的撰写过程中，得到了导师和同学们的大力支持和帮助，在此表示衷心的感谢。

感谢导师在论文选题、研究方法、技术路线等方面给予的悉心指导，使我能够顺利完成本研究工作。感谢实验室的同学们在技术讨论和系统测试方面提供的帮助。

同时，感谢所有为本研究提供支持和帮助的老师、同学和朋友们，正是有了大家的支持，才使得本研究工作得以顺利完成。

## 附录

### 附录A 系统安装部署指南

#### A.1 环境要求

- JDK 17或以上版本
- MySQL 8.0或以上版本
- Redis 6.0或以上版本
- Node.js 16或以上版本

#### A.2 后端部署步骤

1. 创建数据库并导入SQL脚本
2. 修改application.yml配置文件
3. 执行maven打包命令
4. 启动Spring Boot应用

#### A.3 前端部署步骤

1. 安装Node.js依赖
2. 修改API接口地址配置
3. 执行构建命令
4. 部署到Web服务器

### 附录B 系统使用手册

#### B.1 用户登录

1. 打开系统首页
2. 输入用户名和密码
3. 点击登录按钮

#### B.2 项目管理操作

1. 进入项目管理页面
2. 点击新增按钮创建项目
3. 填写项目基本信息
4. 添加项目成员并设置贡献率
5. 上传佐证材料
6. 保存项目信息

### 附录C 数据库设计文档

详细的数据库表结构设计和字段说明请参考系统设计章节的相关内容。

---

**论文完成时间：** 2024年12月

**总页数：** 约50页

**字数统计：** 约30,000字
    private Integer id;                    // 主键ID
    private String name;                   // 项目名称
    private Integer type;                  // 项目类型
    private Integer organizer;             // 主办方（字典）
    private String organizerOther;         // 主办方（不在字典里面）
    private Integer level;                 // 级别（省级，国家级，校级）
    private String author;                 // 第一作者，负责人
    private String member;                 // 成员
    private String rate;                   // 贡献率数组
    private LocalDateTime projectStartTime; // 立项时间
    private LocalDateTime projectEndTime;   // 结项时间
    private Integer status;                // 项目状态
    @TableField(value = "evidence_url", typeHandler = JacksonTypeHandler.class)
    private List<String> evidenceUrl;     // 佐证地址
    private String remark;                 // 备注
}
````
</augment_code_snippet>

**数据模型特点：**
1. **多态字段设计**：organizer字段支持字典值，organizerOther字段支持自定义主办方
2. **JSON字段处理**：evidenceUrl使用JacksonTypeHandler处理JSON数组
3. **时间管理**：支持立项时间和结项时间的完整记录
4. **状态跟踪**：通过status字段实现项目状态管理
5. **成员管理**：member和rate字段配合实现团队成员及贡献率管理

#### 4.1.3 业务逻辑层实现

**服务接口设计：**

<augment_code_snippet path="digital-image/yudao-module-digital/yudao-module-digital-biz/src/main/java/cn/iocoder/yudao/module/digital/service/projects/ProjectsService.java" mode="EXCERPT">
````java
public interface ProjectsService {
    Integer createProjects(@Valid ProjectsSaveReqVO createReqVO);
    void updateProjects(@Valid ProjectsSaveReqVO updateReqVO);
    void deleteProjects(Integer id);
    ProjectsDO getProjects(Integer id);
    PageResult<ProjectsDO> getProjectsPage(ProjectsPageReqVO pageReqVO);
}
````
</augment_code_snippet>

**服务实现类：**

<augment_code_snippet path="digital-image/yudao-module-digital/yudao-module-digital-biz/src/main/java/cn/iocoder/yudao/module/digital/service/projects/ProjectsServiceImpl.java" mode="EXCERPT">
````java
@Service
@Validated
public class ProjectsServiceImpl implements ProjectsService {
    @Resource
    private ProjectsMapper projectsMapper;

    @Override
    public Integer createProjects(ProjectsSaveReqVO createReqVO) {
        ProjectsDO projects = BeanUtils.toBean(createReqVO, ProjectsDO.class);
        projectsMapper.insert(projects);
        return projects.getId();
    }

    @Override
    public void updateProjects(ProjectsSaveReqVO updateReqVO) {
        validateProjectsExists(updateReqVO.getId());
        ProjectsDO updateObj = BeanUtils.toBean(updateReqVO, ProjectsDO.class);
        projectsMapper.updateById(updateObj);
    }

    private void validateProjectsExists(Integer id) {
        if (projectsMapper.selectById(id) == null) {
            throw exception(PROJECTS_NOT_EXISTS);
        }
    }
}
````
</augment_code_snippet>

#### 4.1.4 控制器层实现

项目管理模块的控制器采用RESTful风格设计，提供完整的CRUD操作：

<augment_code_snippet path="digital-image/yudao-module-digital/yudao-module-digital-biz/src/main/java/cn/iocoder/yudao/module/digital/controller/admin/projects/ProjectsController.java" mode="EXCERPT">
````java
@Tag(name = "数字画像 - 项目")
@RestController
@RequestMapping("/digital/projects")
@Validated
public class ProjectsController {
    @Resource
    private ProjectsService projectsService;

    @PostMapping("/create")
    @Operation(summary = "创建项目")
    @PreAuthorize("@ss.hasPermission('digital:projects:create')")
    public CommonResult<Integer> createProjects(@Valid @RequestBody ProjectsSaveReqVO createReqVO) {
        return success(projectsService.createProjects(createReqVO));
    }

    @GetMapping("/page")
    @Operation(summary = "获得项目分页")
    @PreAuthorize("@ss.hasPermission('digital:projects:query')")
    public CommonResult<PageResult<ProjectsRespVO>> getProjectsPage(@Valid ProjectsPageReqVO pageReqVO) {
        PageResult<ProjectsDO> pageResult = projectsService.getProjectsPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, ProjectsRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出项目 Excel")
    @PreAuthorize("@ss.hasPermission('digital:projects:export')")
    public void exportProjectsExcel(@Valid ProjectsPageReqVO pageReqVO, HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ProjectsDO> list = projectsService.getProjectsPage(pageReqVO).getList();
        ExcelUtils.write(response, "项目.xls", "数据", ProjectsRespVO.class,
                        BeanUtils.toBean(list, ProjectsRespVO.class));
    }
}
````
</augment_code_snippet>

#### 4.1.5 前端组件实现

**项目列表页面：**

<augment_code_snippet path="digital-image-ui/src/views/digital/projects/index.vue" mode="EXCERPT">
````vue
<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { ProjectsApi, ProjectsVO } from '@/api/digital/projects'
import ProjectsForm from './ProjectsForm.vue'

defineOptions({ name: 'Projects' })

const loading = ref(false)
const list = ref<ProjectsVO[]>([])
const total = ref(0)
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  type: undefined,
  level: undefined
})

const getList = async () => {
  loading.value = true
  try {
    const data = await ProjectsApi.getProjectsPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

onMounted(() => {
  getList()
})
</script>
````
</augment_code_snippet>

**项目表单组件：**

<augment_code_snippet path="digital-image-ui/src/views/digital/projects/ProjectsForm.vue" mode="EXCERPT">
````vue
<template>
  <el-form ref="formRef" :model="formData" :rules="formRules">
    <el-form-item label="项目名称" prop="name">
      <el-input v-model="formData.name" placeholder="请输入项目名称" />
    </el-form-item>

    <el-form-item label="成员管理">
      <div v-for="(member, index) in formData.memberList" :key="index" class="member-item">
        <el-select v-model="member.userId" placeholder="选择成员" style="width: 200px">
          <el-option v-for="user in userOptions" :key="user.id"
                     :label="user.nickname" :value="user.id" />
        </el-select>
        <el-input-number v-model="member.rate" :min="0" :max="100"
                         placeholder="贡献率" style="width: 120px; margin-left: 10px" />
        <el-button type="danger" @click="removeMember(index)" style="margin-left: 10px">删除</el-button>
      </div>
      <el-button type="primary" @click="addMember" style="margin-top: 10px">添加成员</el-button>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
const addMember = () => {
  formData.value.memberList.push({ userId: undefined, rate: 0 })
}

const removeMember = (index: number) => {
  formData.value.memberList.splice(index, 1)
}
</script>
````
</augment_code_snippet>

### 4.2 竞赛管理模块实现

#### 4.2.1 模块概述

竞赛管理模块用于记录和管理教师指导学生参加各类学科竞赛的情况。该模块支持竞赛信息的完整记录，包括竞赛基本信息、获奖情况、指导教师信息等，为教师的竞赛指导能力评价提供数据支撑。

#### 4.2.2 数据模型实现

竞赛管理模块的核心数据模型CompetitionDO设计如下：

<augment_code_snippet path="digital-image/yudao-module-digital/yudao-module-digital-biz/src/main/java/cn/iocoder/yudao/module/digital/dal/dataobject/competition/CompetitionDO.java" mode="EXCERPT">
````java
@TableName(value = "digital_competition", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class CompetitionDO extends BaseDO {
    @TableId
    private Integer id;                    // 主键ID
    private String name;                   // 竞赛名称
    private Integer type;                  // 比赛级别(A+,A等)
    private Integer organizer;             // 主办方
    private String organizerOther;         // 其他主办方
    private Integer level;                 // 获奖级别(省级,国家级,校级)
    private String author;                 // 第一指导老师
    private String member;                 // 成员列表
    private String rate;                   // 贡献率数组
    private LocalDateTime competitionTime; // 获奖时间
    private Integer rank;                  // 奖项排名
    @TableField(value = "evidence_url", typeHandler = JacksonTypeHandler.class)
    private List<String> evidenceUrl;     // 佐证材料URL
    private String remark;                 // 备注信息
}
````
</augment_code_snippet>

#### 4.2.3 业务逻辑层实现

**服务接口设计：**

<augment_code_snippet path="digital-image/yudao-module-digital/yudao-module-digital-biz/src/main/java/cn/iocoder/yudao/module/digital/service/competition/CompetitionService.java" mode="EXCERPT">
````java
public interface CompetitionService {
    Integer createCompetition(@Valid CompetitionSaveReqVO createReqVO);
    void updateCompetition(@Valid CompetitionSaveReqVO updateReqVO);
    void deleteCompetition(Integer id);
    CompetitionDO getCompetition(Integer id);
    PageResult<CompetitionDO> getCompetitionPage(CompetitionPageReqVO pageReqVO);
}
````
</augment_code_snippet>

**服务实现类：**

<augment_code_snippet path="digital-image/yudao-module-digital/yudao-module-digital-biz/src/main/java/cn/iocoder/yudao/module/digital/service/competition/CompetitionServiceImpl.java" mode="EXCERPT">
````java
@Service
@Validated
public class CompetitionServiceImpl implements CompetitionService {
    @Resource
    private CompetitionMapper competitionMapper;

    @Override
    public Integer createCompetition(CompetitionSaveReqVO createReqVO) {
        // 数据验证
        validateCompetitionData(createReqVO);

        // 转换数据对象
        CompetitionDO competition = BeanUtils.toBean(createReqVO, CompetitionDO.class);

        // 保存到数据库
        competitionMapper.insert(competition);

        return competition.getId();
    }

    private void validateCompetitionData(CompetitionSaveReqVO reqVO) {
        // 验证竞赛名称不能为空
        if (StrUtil.isBlank(reqVO.getName())) {
            throw exception(COMPETITION_NAME_NOT_NULL);
        }
        // 验证获奖时间不能为空
        if (reqVO.getCompetitionTime() == null) {
            throw exception(COMPETITION_TIME_NOT_NULL);
        }
    }
}
````
</augment_code_snippet>

#### 4.2.4 控制器层实现

<augment_code_snippet path="digital-image/yudao-module-digital/yudao-module-digital-biz/src/main/java/cn/iocoder/yudao/module/digital/controller/admin/competition/CompetitionController.java" mode="EXCERPT">
````java
@Tag(name = "数字画像 - 竞赛")
@RestController
@RequestMapping("/digital/competition")
@Validated
public class CompetitionController {
    @Resource
    private CompetitionService competitionService;

    @PostMapping("/create")
    @Operation(summary = "创建竞赛")
    @PreAuthorize("@ss.hasPermission('digital:competition:create')")
    public CommonResult<Integer> createCompetition(@Valid @RequestBody CompetitionSaveReqVO createReqVO) {
        return success(competitionService.createCompetition(createReqVO));
    }

    @GetMapping("/page")
    @Operation(summary = "获得竞赛分页")
    @PreAuthorize("@ss.hasPermission('digital:competition:query')")
    public CommonResult<PageResult<CompetitionRespVO>> getCompetitionPage(@Valid CompetitionPageReqVO pageReqVO) {
        PageResult<CompetitionDO> pageResult = competitionService.getCompetitionPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CompetitionRespVO.class));
    }
}
````
</augment_code_snippet>

### 4.3 用户管理模块实现

#### 4.3.1 模块概述

用户管理模块是系统的基础管理功能，负责管理系统中的所有用户信息，包括用户基本信息、权限分配、状态管理等。该模块为整个系统提供用户认证和授权的基础支撑。

#### 4.3.2 数据模型实现

<augment_code_snippet path="digital-image/yudao-module-system/yudao-module-system-biz/src/main/java/cn/iocoder/yudao/module/system/dal/dataobject/user/AdminUserDO.java" mode="EXCERPT">
````java
@TableName(value = "system_users", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class AdminUserDO extends TenantBaseDO {
    @TableId
    private Long id;                       // 用户ID
    private String username;               // 用户账号
    private String password;               // 加密后的密码
    private String nickname;               // 用户昵称
    private String remark;                 // 备注
    private Long deptId;                   // 部门ID
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Set<Long> postIds;             // 岗位编号数组
    private String email;                  // 用户邮箱
    private String mobile;                 // 手机号码
    private Integer sex;                   // 用户性别
    private String avatar;                 // 用户头像
    private Integer status;                // 帐号状态
    private String loginIp;                // 最后登录IP
    private LocalDateTime loginDate;       // 最后登录时间
}
````
</augment_code_snippet>

#### 4.3.3 业务逻辑层实现

<augment_code_snippet path="digital-image/yudao-module-system/yudao-module-system-biz/src/main/java/cn/iocoder/yudao/module/system/service/user/AdminUserServiceImpl.java" mode="EXCERPT">
````java
@Service
@Validated
public class AdminUserServiceImpl implements AdminUserService {
    @Resource
    private AdminUserMapper userMapper;
    @Resource
    private DeptService deptService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createUser(UserSaveReqVO createReqVO) {
        // 校验用户存在
        validateUserForCreateOrUpdate(null, createReqVO.getUsername(),
                                     createReqVO.getMobile(), createReqVO.getEmail(),
                                     createReqVO.getDeptId(), createReqVO.getPostIds());

        // 插入用户
        AdminUserDO user = BeanUtils.toBean(createReqVO, AdminUserDO.class);
        user.setStatus(CommonStatusEnum.ENABLE.getStatus()); // 默认开启
        user.setPassword(encodePassword(createReqVO.getPassword())); // 加密密码
        userMapper.insert(user);

        return user.getId();
    }

    private void validateUserForCreateOrUpdate(Long id, String username, String mobile,
                                             String email, Long deptId, Set<Long> postIds) {
        // 校验用户名唯一
        validateUsernameUnique(id, username);
        // 校验手机号唯一
        validateMobileUnique(id, mobile);
        // 校验邮箱唯一
        validateEmailUnique(id, email);
        // 校验部门处于开启状态
        deptService.validateDeptList(singleton(deptId));
        // 校验岗位处于开启状态
        postService.validatePostList(postIds);
    }
}
````
</augment_code_snippet>

### 4.4 部门管理模块实现

#### 4.4.1 模块概述

部门管理模块用于管理教育机构的组织架构，支持多级部门结构的创建和维护。该模块为用户管理和权限控制提供组织架构基础，是实现数据权限隔离的重要组件。

#### 4.4.2 数据模型实现

<augment_code_snippet path="digital-image/yudao-module-system/yudao-module-system-biz/src/main/java/cn/iocoder/yudao/module/system/dal/dataobject/dept/DeptDO.java" mode="EXCERPT">
````java
@TableName("system_dept")
@Data
@EqualsAndHashCode(callSuper = true)
public class DeptDO extends TenantBaseDO {
    public static final Long PARENT_ID_ROOT = 0L;

    @TableId
    private Long id;                       // 部门ID
    private String name;                   // 部门名称
    private Long parentId;                 // 父部门ID
    private Integer sort;                  // 显示顺序
    private Long leaderUserId;             // 负责人
    private String phone;                  // 联系电话
    private String email;                  // 邮箱
    private Integer status;                // 部门状态
}
````
</augment_code_snippet>

#### 4.4.3 业务逻辑层实现

<augment_code_snippet path="digital-image/yudao-module-system/yudao-module-system-biz/src/main/java/cn/iocoder/yudao/module/system/service/dept/DeptServiceImpl.java" mode="EXCERPT">
````java
@Service
@Validated
public class DeptServiceImpl implements DeptService {
    @Resource
    private DeptMapper deptMapper;

    @Override
    @CacheEvict(cacheNames = RedisKeyConstants.DEPT_CHILDREN_ID_LIST, allEntries = true)
    public Long createDept(DeptSaveReqVO createReqVO) {
        if (createReqVO.getParentId() == null) {
            createReqVO.setParentId(DeptDO.PARENT_ID_ROOT);
        }
        // 校验父部门的有效性
        validateParentDept(null, createReqVO.getParentId());
        // 校验部门名的唯一性
        validateDeptNameUnique(null, createReqVO.getParentId(), createReqVO.getName());

        // 插入部门
        DeptDO dept = BeanUtils.toBean(createReqVO, DeptDO.class);
        deptMapper.insert(dept);
        return dept.getId();
    }

    private void validateParentDept(Long id, Long parentId) {
        if (parentId == null || DeptDO.PARENT_ID_ROOT.equals(parentId)) {
            return;
        }
        // 1. 不能设置自己为父部门
        if (Objects.equals(id, parentId)) {
            throw exception(DEPT_PARENT_ERROR);
        }
        // 2. 父部门不存在
        DeptDO dept = deptMapper.selectById(parentId);
        if (dept == null) {
            throw exception(DEPT_PARENT_NOT_EXITS);
        }
        // 3. 递归校验父部门，如果父部门是自己的子部门，则报错，避免形成环路
        if (id != null) {
            for (int i = 0; i < Short.MAX_VALUE; i++) {
                if (Objects.equals(dept.getParentId(), id)) {
                    throw exception(DEPT_PARENT_IS_CHILD);
                }
                dept = deptMapper.selectById(dept.getParentId());
                if (dept == null || DeptDO.PARENT_ID_ROOT.equals(dept.getParentId())) {
                    break;
                }
            }
        }
    }
}
````
</augment_code_snippet>

#### 4.4.4 前端组件实现

<augment_code_snippet path="digital-image-ui/src/views/system/dept/index.vue" mode="EXCERPT">
````vue
<template>
  <ContentWrap>
    <el-table v-loading="loading" :data="list" row-key="id" :default-expand-all="isExpandAll">
      <el-table-column prop="name" label="部门名称" />
      <el-table-column prop="leader" label="负责人">
        <template #default="scope">
          {{ userList.find((user) => user.id === scope.row.leaderUserId)?.nickname }}
        </template>
      </el-table-column>
      <el-table-column prop="sort" label="排序" />
      <el-table-column prop="status" label="状态">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.COMMON_STATUS" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button link type="primary" @click="openForm('update', scope.row.id)">修改</el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
  </ContentWrap>
</template>

<script setup lang="ts">
import { handleTree } from '@/utils/tree'
import * as DeptApi from '@/api/system/dept'

const getList = async () => {
  loading.value = true
  try {
    const data = await DeptApi.getDeptPage(queryParams)
    list.value = handleTree(data)
  } finally {
    loading.value = false
  }
}
</script>
````
</augment_code_snippet>

### 4.5 角色管理模块实现

#### 4.5.1 模块概述

角色管理模块是系统权限控制的核心组件，基于RBAC（基于角色的访问控制）模型设计。该模块负责定义和管理系统中的各种角色，为用户分配相应的权限，实现细粒度的权限控制。

#### 4.5.2 数据模型实现

<augment_code_snippet path="digital-image/yudao-module-system/yudao-module-system-biz/src/main/java/cn/iocoder/yudao/module/system/dal/dataobject/permission/RoleDO.java" mode="EXCERPT">
````java
@TableName("system_role")
@Data
@EqualsAndHashCode(callSuper = true)
public class RoleDO extends TenantBaseDO {
    @TableId
    private Long id;                       // 角色ID
    private String name;                   // 角色名称
    private String code;                   // 角色权限字符串
    private Integer sort;                  // 显示顺序
    private Integer dataScope;             // 数据范围
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Set<Long> dataScopeDeptIds;    // 数据范围(指定部门数组)
    private Integer status;                // 角色状态
    private Integer type;                  // 角色类型
    private String remark;                 // 备注
}
````
</augment_code_snippet>

#### 4.5.3 业务逻辑层实现

<augment_code_snippet path="digital-image/yudao-module-system/yudao-module-system-biz/src/main/java/cn/iocoder/yudao/module/system/service/permission/RoleServiceImpl.java" mode="EXCERPT">
````java
@Service
@Validated
public class RoleServiceImpl implements RoleService {
    @Resource
    private RoleMapper roleMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createRole(RoleSaveReqVO createReqVO, Integer type) {
        // 1. 校验角色
        validateRoleDuplicate(createReqVO.getName(), createReqVO.getCode(), null);

        // 2. 插入到数据库
        RoleDO role = BeanUtils.toBean(createReqVO, RoleDO.class)
                .setType(ObjectUtil.defaultIfNull(type, RoleTypeEnum.CUSTOM.getType()))
                .setStatus(ObjUtil.defaultIfNull(createReqVO.getStatus(), CommonStatusEnum.ENABLE.getStatus()))
                .setDataScope(DataScopeEnum.ALL.getScope()); // 默认可查看所有数据
        roleMapper.insert(role);

        return role.getId();
    }

    @Override
    public void updateRoleDataScope(Long id, Integer dataScope, Set<Long> dataScopeDeptIds) {
        // 校验是否可以更新
        validateRoleForUpdate(id);

        // 更新数据范围
        RoleDO updateObj = new RoleDO();
        updateObj.setId(id);
        updateObj.setDataScope(dataScope);
        updateObj.setDataScopeDeptIds(dataScopeDeptIds);
        roleMapper.updateById(updateObj);
    }

    private void validateRoleDuplicate(String name, String code, Long id) {
        // 0. 超级管理员，不允许创建
        if (isBuiltinRole(code)) {
            throw exception(ROLE_ADMIN_CODE_ERROR, code);
        }
        // 1. 该 name 名字被其它角色所使用
        RoleDO role = roleMapper.selectByName(name);
        if (role != null && !role.getId().equals(id)) {
            throw exception(ROLE_NAME_DUPLICATE, name);
        }
        // 2. 该 code 编码被其它角色所使用
        role = roleMapper.selectByCode(code);
        if (role != null && !role.getId().equals(id)) {
            throw exception(ROLE_CODE_DUPLICATE, code);
        }
    }
}
````
</augment_code_snippet>

#### 4.5.4 控制器层实现

<augment_code_snippet path="digital-image/yudao-module-system/yudao-module-system-biz/src/main/java/cn/iocoder/yudao/module/system/controller/admin/permission/RoleController.java" mode="EXCERPT">
````java
@Tag(name = "管理后台 - 角色")
@RestController
@RequestMapping("/system/role")
@Validated
public class RoleController {
    @Resource
    private RoleService roleService;

    @PostMapping("/create")
    @Operation(summary = "创建角色")
    @PreAuthorize("@ss.hasPermission('system:role:create')")
    public CommonResult<Long> createRole(@Valid @RequestBody RoleSaveReqVO createReqVO) {
        return success(roleService.createRole(createReqVO, null));
    }

    @GetMapping("/page")
    @Operation(summary = "获得角色分页")
    @PreAuthorize("@ss.hasPermission('system:role:query')")
    public CommonResult<PageResult<RoleRespVO>> getRolePage(RolePageReqVO pageReqVO) {
        PageResult<RoleDO> pageResult = roleService.getRolePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, RoleRespVO.class));
    }

    @PutMapping("/data-scope")
    @Operation(summary = "修改角色数据权限")
    @PreAuthorize("@ss.hasPermission('system:permission:assign-role-data-scope')")
    public CommonResult<Boolean> updateRoleDataScope(@Valid @RequestBody RoleDataScopeReqVO reqVO) {
        roleService.updateRoleDataScope(reqVO.getId(), reqVO.getDataScope(), reqVO.getDataScopeDeptIds());
        return success(true);
    }
}
````
</augment_code_snippet>

## 4. 前端实现

### 4.1 组件化设计

前端采用Vue3的Composition API和组件化设计：

```vue
<template>
  <div class="competition-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>竞赛管理</span>
          <el-button type="primary" @click="openForm('create')">
            新增竞赛
          </el-button>
        </div>
      </template>
      
      <!-- 搜索表单 -->
      <el-form :model="queryParams" ref="queryFormRef" inline>
        <el-form-item label="竞赛名称" prop="name">
          <el-input v-model="queryParams.name" placeholder="请输入竞赛名称" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleQuery">搜索</el-button>
          <el-button @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 数据表格 -->
      <el-table :data="list" v-loading="loading">
        <el-table-column prop="name" label="竞赛名称" />
        <el-table-column prop="type" label="比赛级别" />
        <el-table-column prop="level" label="获奖级别" />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button link type="primary" @click="openForm('update', scope.row.id)">
              编辑
            </el-button>
            <el-button link type="danger" @click="handleDelete(scope.row.id)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 表单弹窗 -->
    <CompetitionForm ref="formRef" @success="getList" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { CompetitionApi, CompetitionVO } from '@/api/digital/competition'
import CompetitionForm from './CompetitionForm.vue'

// 响应式数据
const loading = ref(false)
const list = ref<CompetitionVO[]>([])
const total = ref(0)

const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: undefined
})

// 查询列表
const getList = async () => {
  loading.value = true
  try {
    const data = await CompetitionApi.getCompetitionPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

// 搜索
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

// 组件挂载时获取数据
onMounted(() => {
  getList()
})
</script>
```

### 4.2 状态管理

使用Pinia进行全局状态管理：

```typescript
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 状态
  const currentSize = ref('default')
  const greyMode = ref(false)
  const isDark = ref(false)
  
  // 计算属性
  const getCurrentSize = computed(() => currentSize.value)
  const getGreyMode = computed(() => greyMode.value)
  
  // 方法
  const setCurrentSize = (size: string) => {
    currentSize.value = size
  }
  
  const setGreyMode = (mode: boolean) => {
    greyMode.value = mode
  }
  
  const setIsDark = (dark: boolean) => {
    isDark.value = dark
  }
  
  return {
    currentSize,
    greyMode,
    isDark,
    getCurrentSize,
    getGreyMode,
    setCurrentSize,
    setGreyMode,
    setIsDark
  }
})
```

## 5. 数据库设计

### 5.1 核心表结构

系统主要包含以下核心数据表：

1. **digital_competition** - 竞赛信息表
2. **digital_projects** - 项目信息表  
3. **system_users** - 用户信息表
4. **system_role** - 角色信息表
5. **ai_image** - AI绘图记录表
6. **ai_chat_message** - AI对话记录表

### 5.2 数据关系设计

系统采用规范化的数据库设计，通过外键关联建立表间关系，支持多租户架构，每个表都包含租户ID字段进行数据隔离。

## 6. 系统特色与创新

### 6.1 AI技术集成

系统集成了多种AI技术：
- 支持OpenAI、通义千问、文心一言等多个AI平台
- 提供AI绘图、AI对话、AI思维导图等功能
- 智能分析教师画像数据，生成可视化报告

### 6.2 模块化架构

采用微服务架构设计，各功能模块独立部署，便于扩展和维护：
- 数字画像模块（digital）
- AI功能模块（ai）  
- 系统管理模块（system）
- 基础设施模块（infra）

### 6.3 多租户支持

系统原生支持多租户架构，可以为不同的教育机构提供独立的数据空间和功能定制。

## 7. 系统部署与运维

### 7.1 部署架构

系统支持Docker容器化部署，提供了完整的部署脚本和配置文件：

```yaml
version: '3.8'
services:
  yudao-server:
    image: yudao-server:latest
    ports:
      - "48080:48080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - MYSQL_HOST=mysql
      - REDIS_HOST=redis
    depends_on:
      - mysql
      - redis
      
  mysql:
    image: mysql:5.7
    environment:
      - MYSQL_ROOT_PASSWORD=123456
      - MYSQL_DATABASE=digital-image
    volumes:
      - mysql_data:/var/lib/mysql
      
  redis:
    image: redis:6.2
    volumes:
      - redis_data:/data
```

### 7.2 监控与日志

系统集成了完善的监控和日志功能：
- API访问日志记录
- 系统异常日志监控
- 性能指标监控
- 用户行为分析

## 8. 总结与展望

本文设计并实现了一个基于现代Web技术栈的教师数字画像系统，该系统具有以下特点：

1. **技术先进性**：采用Vue3、Spring Boot等主流技术栈
2. **功能完整性**：涵盖竞赛管理、项目管理、AI分析等核心功能
3. **架构合理性**：微服务架构，支持高并发和横向扩展
4. **用户体验**：现代化的UI设计，良好的交互体验

未来可以在以下方面进一步完善：
- 增加更多的数据分析维度
- 集成更多的AI功能
- 优化系统性能和用户体验
- 扩展移动端支持

该系统为教育机构的教师管理和评价提供了有效的技术支撑，具有良好的应用前景和推广价值。

## 参考文献

[1] Spring Boot官方文档. https://spring.io/projects/spring-boot
[2] Vue.js官方文档. https://vuejs.org/
[3] Element Plus组件库. https://element-plus.org/
[4] MyBatis Plus官方文档. https://baomidou.com/
[5] Spring AI项目. https://spring.io/projects/spring-ai
