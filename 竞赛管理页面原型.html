<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>竞赛管理 - 数字画像系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #409eff;
            margin-bottom: 10px;
        }
        
        .breadcrumb {
            color: #666;
            font-size: 14px;
        }
        
        .stats-cards {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-value {
            font-size: 32px;
            font-weight: bold;
            color: #409eff;
            margin-bottom: 8px;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        
        .search-form {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
            align-items: center;
        }
        
        .form-item {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .form-item label {
            min-width: 80px;
            font-weight: 500;
        }
        
        .form-item input, .form-item select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            width: 200px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-primary { background: #409eff; color: white; }
        .btn-default { background: #f4f4f5; color: #606266; }
        .btn-success { background: #67c23a; color: white; }
        .btn-danger { background: #f56c6c; color: white; }
        .btn-warning { background: #e6a23c; color: white; }
        .btn-info { background: #909399; color: white; }
        
        .btn:hover { opacity: 0.8; }
        
        .toolbar {
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .table-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }
        
        tr:hover {
            background: #f5f7fa;
        }
        
        .award-tag {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .award-gold {
            background: #fff7e6;
            color: #fa8c16;
            border: 1px solid #ffd591;
        }
        
        .award-silver {
            background: #f6f6f6;
            color: #595959;
            border: 1px solid #d9d9d9;
        }
        
        .award-bronze {
            background: #fff2e8;
            color: #d4380d;
            border: 1px solid #ffbb96;
        }
        
        .level-tag {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .level-national {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        
        .level-provincial {
            background: #e6f7ff;
            color: #1890ff;
            border: 1px solid #91d5ff;
        }
        
        .level-school {
            background: #fff0f6;
            color: #eb2f96;
            border: 1px solid #ffadd2;
        }
        
        .certificate-btn {
            padding: 4px 8px;
            background: #f0f9ff;
            color: #1890ff;
            border: 1px solid #91d5ff;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .certificate-btn:hover {
            background: #e6f7ff;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            gap: 10px;
        }
        
        .page-btn {
            padding: 6px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
        }
        
        .page-btn.active {
            background: #409eff;
            color: white;
            border-color: #409eff;
        }
        
        .checkbox {
            width: 16px;
            height: 16px;
        }
        
        .action-buttons {
            display: flex;
            gap: 5px;
        }
        
        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <h1>竞赛管理</h1>
            <div class="breadcrumb">系统管理 > 竞赛管理 > 竞赛列表</div>
        </div>
        
        <!-- 统计卡片 -->
        <div class="stats-cards">
            <div class="stat-card">
                <div class="stat-value">156</div>
                <div class="stat-label">竞赛总数</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">23</div>
                <div class="stat-label">国家级获奖</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">45</div>
                <div class="stat-label">省级获奖</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">88</div>
                <div class="stat-label">校级获奖</div>
            </div>
        </div>
        
        <!-- 搜索表单 -->
        <div class="search-form">
            <div class="form-row">
                <div class="form-item">
                    <label>竞赛名称:</label>
                    <input type="text" placeholder="请输入竞赛名称">
                </div>
                <div class="form-item">
                    <label>竞赛类型:</label>
                    <select>
                        <option value="">请选择竞赛类型</option>
                        <option value="1">学科竞赛</option>
                        <option value="2">创新创业</option>
                        <option value="3">技能竞赛</option>
                    </select>
                </div>
                <div class="form-item">
                    <label>竞赛级别:</label>
                    <select>
                        <option value="">请选择竞赛级别</option>
                        <option value="1">国家级</option>
                        <option value="2">省级</option>
                        <option value="3">校级</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-item">
                    <label>获奖等级:</label>
                    <select>
                        <option value="">请选择获奖等级</option>
                        <option value="1">一等奖</option>
                        <option value="2">二等奖</option>
                        <option value="3">三等奖</option>
                    </select>
                </div>
                <div class="form-item">
                    <label>获奖时间:</label>
                    <input type="date">
                </div>
                <div class="form-item">
                    <button class="btn btn-primary">查询</button>
                    <button class="btn btn-default">重置</button>
                </div>
            </div>
        </div>
        
        <!-- 工具栏 -->
        <div class="toolbar">
            <button class="btn btn-success">新增竞赛</button>
            <button class="btn btn-danger">批量删除</button>
            <button class="btn btn-info">导出数据</button>
            <button class="btn btn-warning">统计分析</button>
        </div>
        
        <!-- 数据表格 -->
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th><input type="checkbox" class="checkbox"></th>
                        <th>竞赛名称</th>
                        <th>竞赛类型</th>
                        <th>竞赛级别</th>
                        <th>获奖等级</th>
                        <th>指导教师</th>
                        <th>获奖时间</th>
                        <th>获奖证书</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><input type="checkbox" class="checkbox"></td>
                        <td>全国大学生数学建模竞赛</td>
                        <td>学科竞赛</td>
                        <td><span class="level-tag level-national">国家级</span></td>
                        <td><span class="award-tag award-gold">一等奖</span></td>
                        <td>张教授, 李教授</td>
                        <td>2023-11-15</td>
                        <td><button class="certificate-btn">预览</button></td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-primary btn-small">编辑</button>
                                <button class="btn btn-info btn-small">详情</button>
                                <button class="btn btn-danger btn-small">删除</button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td><input type="checkbox" class="checkbox"></td>
                        <td>省大学生程序设计竞赛</td>
                        <td>学科竞赛</td>
                        <td><span class="level-tag level-provincial">省级</span></td>
                        <td><span class="award-tag award-silver">二等奖</span></td>
                        <td>王教授</td>
                        <td>2023-10-20</td>
                        <td><button class="certificate-btn">预览</button></td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-primary btn-small">编辑</button>
                                <button class="btn btn-info btn-small">详情</button>
                                <button class="btn btn-danger btn-small">删除</button>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td><input type="checkbox" class="checkbox"></td>
                        <td>校创新创业大赛</td>
                        <td>创新创业</td>
                        <td><span class="level-tag level-school">校级</span></td>
                        <td><span class="award-tag award-bronze">三等奖</span></td>
                        <td>刘教授, 陈教授</td>
                        <td>2023-09-30</td>
                        <td><button class="certificate-btn">预览</button></td>
                        <td>
                            <div class="action-buttons">
                                <button class="btn btn-primary btn-small">编辑</button>
                                <button class="btn btn-info btn-small">详情</button>
                                <button class="btn btn-danger btn-small">删除</button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
            
            <!-- 分页 -->
            <div class="pagination">
                <button class="page-btn">上一页</button>
                <button class="page-btn active">1</button>
                <button class="page-btn">2</button>
                <button class="page-btn">3</button>
                <button class="page-btn">下一页</button>
                <span style="margin-left: 20px;">共 156 条记录，每页 10 条</span>
            </div>
        </div>
    </div>
</body>
</html>
