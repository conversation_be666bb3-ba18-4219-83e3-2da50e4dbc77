# 数字画像系统数据库语句

根据《基于Spring Boot和Vue3的教师数字画像系统设计与实现》技术论文，本文档整理了系统中相对应的数据库表结构和SQL语句。

## 目录

1. [核心业务模块数据库表](#核心业务模块数据库表)
   - [1.1 项目管理表 (digital_projects)](#11-项目管理表-digital_projects)
   - [1.2 竞赛管理表 (digital_competition)](#12-竞赛管理表-digital_competition)

2. [系统管理模块数据库表](#系统管理模块数据库表)
   - [2.1 用户管理表 (system_users)](#21-用户管理表-system_users)
   - [2.2 部门管理表 (system_dept)](#22-部门管理表-system_dept)
   - [2.3 角色管理表 (system_role)](#23-角色管理表-system_role)
   - [2.4 用户角色关联表 (system_user_role)](#24-用户角色关联表-system_user_role)
   - [2.5 菜单权限表 (system_menu)](#25-菜单权限表-system_menu)
   - [2.6 角色菜单关联表 (system_role_menu)](#26-角色菜单关联表-system_role_menu)
   - [2.7 岗位信息表 (system_post)](#27-岗位信息表-system_post)
   - [2.8 用户会话表 (system_user_session)](#28-用户会话表-system_user_session)

3. [基础支撑模块数据库表](#基础支撑模块数据库表)
   - [3.1 文件管理表 (infra_file)](#31-文件管理表-infra_file)
   - [3.2 API访问日志表 (infra_api_access_log)](#32-api访问日志表-infra_api_access_log)
   - [3.3 API错误日志表 (infra_api_error_log)](#33-api错误日志表-infra_api_error_log)
   - [3.4 多租户表 (system_tenant)](#34-多租户表-system_tenant)

---

## 核心业务模块数据库表

### 1.1 项目管理表 (digital_projects)

**表说明：** 用于存储教师参与的各类科研项目和教学项目信息，支持项目全生命周期管理。

```sql
-- ----------------------------
-- Table structure for digital_projects
-- ----------------------------
DROP TABLE IF EXISTS `digital_projects`;
CREATE TABLE `digital_projects`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目名字',
  `type` tinyint(4) NOT NULL COMMENT '项目类型',
  `organizer` tinyint(4) NULL DEFAULT NULL COMMENT '主办方（字典）',
  `organizer_other` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主办方（不在字典里面）',
  `level` tinyint(4) NOT NULL COMMENT '级别（省级，国家级，校级）',
  `author` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '第一作者，负责人',
  `member` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '成员',
  `rate` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '贡献率数组，0号元素是负责人，后面的是成员',
  `project_start_time` datetime NOT NULL COMMENT '立项时间',
  `project_end_time` datetime NULL DEFAULT NULL COMMENT '结项时间',
  `status` tinyint(4) NOT NULL COMMENT '状态',
  `evidence_url` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '佐证地址',
  `creator` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `updater` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `reserved1` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '保留字段',
  `reserved2` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '保留字段',
  `reserved3` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '保留字段',
  `reserved4` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '保留字段',
  `reserved5` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '保留字段',
  `tenant_id` bigint(20) NULL DEFAULT NULL COMMENT '租户id',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 44 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '项目表' ROW_FORMAT = DYNAMIC;
```

**字段说明：**
- `id`: 项目唯一标识
- `name`: 项目名称
- `type`: 项目类型（通过字典管理）
- `organizer`: 主办方（字典值）
- `organizer_other`: 其他主办方（自定义输入）
- `level`: 项目级别（省级、国家级、校级等）
- `author`: 第一作者/项目负责人
- `member`: 项目成员列表
- `rate`: 贡献率分配
- `project_start_time`: 立项时间
- `project_end_time`: 结项时间
- `status`: 项目状态（进行中、已结项等）
- `evidence_url`: 佐证材料文件URL
- `tenant_id`: 多租户支持
- `deleted`: 软删除标记

### 1.2 竞赛管理表 (digital_competition)

**表说明：** 用于记录和管理教师指导学生参加各类学科竞赛的情况，包括竞赛信息、获奖情况等。

```sql
-- ----------------------------
-- Table structure for digital_competition
-- ----------------------------
DROP TABLE IF EXISTS `digital_competition`;
CREATE TABLE `digital_competition`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '竞赛名字',
  `type` tinyint(4) NOT NULL COMMENT '比赛级别(A+,A等等，字典)',
  `organizer` tinyint(4) NULL DEFAULT NULL COMMENT '主办方（字典）',
  `organizer_other` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主办方（不在字典里面）',
  `level` tinyint(4) NOT NULL COMMENT '获奖级别（省级，国家级，校级）',
  `author` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '第一指导老师',
  `member` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '成员',
  `rate` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '贡献率数组，0号元素是第一指导，后面的是成员',
  `competition_organizer` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '竞赛负责人',
  `competition_time` datetime NOT NULL COMMENT '获奖时间',
  `rank` tinyint(4) NOT NULL COMMENT '排名',
  `evidence_url` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '佐证地址',
  `creator` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `updater` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `reserved1` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '保留字段',
  `reserved2` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '保留字段',
  `reserved3` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '保留字段',
  `reserved4` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '保留字段',
  `reserved5` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '保留字段',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NULL DEFAULT NULL COMMENT '租户',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 20 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '竞赛表' ROW_FORMAT = DYNAMIC;
```

**字段说明：**
- `id`: 竞赛记录唯一标识
- `name`: 竞赛名称
- `type`: 比赛级别（A+、A等，通过字典管理）
- `organizer`: 主办方（字典值）
- `organizer_other`: 其他主办方（自定义输入）
- `level`: 获奖级别（省级、国家级、校级）
- `author`: 第一指导教师
- `member`: 指导教师成员
- `rate`: 指导教师贡献率分配
- `competition_organizer`: 竞赛负责人
- `competition_time`: 获奖时间
- `rank`: 获奖排名
- `evidence_url`: 获奖证书等佐证材料URL
- `tenant_id`: 多租户支持
- `deleted`: 软删除标记

---

## 系统管理模块数据库表

### 2.1 用户管理表 (system_users)

**表说明：** 系统用户基础信息表，支持多租户架构，提供完整的用户管理功能。

```sql
CREATE TABLE IF NOT EXISTS "system_users" (
    "id" bigint not null GENERATED BY DEFAULT AS IDENTITY,
    "username" varchar(30) not null,
    "password" varchar(100) not null default '',
    "nickname" varchar(30) not null,
    "remark" varchar(500) default null,
    "dept_id" bigint default null,
    "post_ids" varchar(255) default null,
    "email" varchar(50) default '',
    "mobile" varchar(11) default '',
    "sex" tinyint default '0',
    "avatar" varchar(100) default '',
    "status" tinyint not null default '0',
    "login_ip" varchar(50) default '',
    "login_date" timestamp default null,
    "creator" varchar(64) default '',
    "create_time" timestamp not null default current_timestamp,
    "updater" varchar(64) default '',
    "update_time" timestamp not null default current_timestamp,
    "deleted" bit not null default false,
    "tenant_id" bigint not null default '0',
    PRIMARY KEY ("id")
) COMMENT '用户信息表';
```

**字段说明：**
- `id`: 用户唯一标识
- `username`: 用户名（登录账号）
- `password`: 加密后的密码
- `nickname`: 用户昵称
- `remark`: 用户备注
- `dept_id`: 所属部门ID
- `post_ids`: 岗位ID列表
- `email`: 邮箱地址
- `mobile`: 手机号码
- `sex`: 性别（0女1男）
- `avatar`: 头像URL
- `status`: 用户状态（0禁用1启用）
- `login_ip`: 最后登录IP
- `login_date`: 最后登录时间
- `tenant_id`: 多租户支持

### 2.2 部门管理表 (system_dept)

**表说明：** 部门层级管理表，支持多级部门结构的创建和维护。

```sql
CREATE TABLE IF NOT EXISTS "system_dept" (
    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "name" varchar(30) NOT NULL DEFAULT '',
    "parent_id" bigint NOT NULL DEFAULT '0',
    "sort" int NOT NULL DEFAULT '0',
    "leader_user_id" bigint DEFAULT NULL,
    "phone" varchar(11) DEFAULT NULL,
    "email" varchar(50) DEFAULT NULL,
    "status" tinyint NOT NULL,
    "creator" varchar(64) DEFAULT '',
    "create_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updater" varchar(64) DEFAULT '',
    "update_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted" bit NOT NULL DEFAULT FALSE,
    "tenant_id" bigint not null default  '0',
    PRIMARY KEY ("id")
) COMMENT '部门表';
```

**字段说明：**
- `id`: 部门唯一标识
- `name`: 部门名称
- `parent_id`: 上级部门ID（支持多级结构）
- `sort`: 显示排序
- `leader_user_id`: 部门负责人用户ID
- `phone`: 部门联系电话
- `email`: 部门邮箱
- `status`: 部门状态（启用/禁用）
- `tenant_id`: 多租户支持

### 2.3 角色管理表 (system_role)

**表说明：** 角色权限管理表，实现基于RBAC模型的权限控制体系。

```sql
CREATE TABLE IF NOT EXISTS "system_role" (
    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "name" varchar(30) NOT NULL,
    "code" varchar(100) NOT NULL,
    "sort" int NOT NULL,
    "data_scope" tinyint NOT NULL DEFAULT '1',
    "data_scope_dept_ids" varchar(500) NOT NULL DEFAULT '',
    "status" tinyint NOT NULL,
    "type" tinyint NOT NULL,
    "remark" varchar(500) DEFAULT NULL,
    "creator" varchar(64) DEFAULT '',
    "create_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updater" varchar(64) DEFAULT '',
    "update_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted" bit NOT NULL DEFAULT FALSE,
    "tenant_id" bigint not null default  '0',
    PRIMARY KEY ("id")
) COMMENT '角色信息表';
```

**字段说明：**
- `id`: 角色唯一标识
- `name`: 角色名称
- `code`: 角色权限字符串
- `sort`: 显示顺序
- `data_scope`: 数据范围（1全部数据权限 2自定数据权限 3本部门数据权限 4本部门及以下数据权限）
- `data_scope_dept_ids`: 数据范围部门数组
- `status`: 角色状态（0禁用1启用）
- `type`: 角色类型
- `remark`: 备注
- `tenant_id`: 多租户支持

### 2.4 用户角色关联表 (system_user_role)

**表说明：** 用户和角色的多对多关联表，实现用户角色分配。

```sql
CREATE TABLE IF NOT EXISTS "system_user_role" (
     "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
     "user_id" bigint NOT NULL,
     "role_id" bigint NOT NULL,
     "creator" varchar(64) DEFAULT '',
     "create_time" timestamp DEFAULT NULL,
     "updater" varchar(64) DEFAULT '',
     "update_time" timestamp DEFAULT NULL,
     "deleted" bit DEFAULT FALSE,
    "tenant_id" bigint not null default  '0',
    PRIMARY KEY ("id")
) COMMENT '用户和角色关联表';
```

**字段说明：**
- `id`: 关联记录唯一标识
- `user_id`: 用户ID
- `role_id`: 角色ID
- `tenant_id`: 多租户支持

### 2.5 菜单权限表 (system_menu)

**表说明：** 系统菜单权限表，支持多级菜单结构和权限控制。

```sql
CREATE TABLE IF NOT EXISTS "system_menu" (
    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "name" varchar(50) NOT NULL,
    "permission" varchar(100) NOT NULL DEFAULT '',
    "type" tinyint NOT NULL,
    "sort" int NOT NULL DEFAULT '0',
    "parent_id" bigint NOT NULL DEFAULT '0',
    "path" varchar(200) DEFAULT '',
    "icon" varchar(100) DEFAULT '#',
    "component" varchar(255) DEFAULT NULL,
    "component_name" varchar(255) DEFAULT NULL,
    "status" tinyint NOT NULL DEFAULT '0',
    "visible" bit NOT NULL DEFAULT TRUE,
    "keep_alive" bit NOT NULL DEFAULT TRUE,
    "always_show" bit NOT NULL DEFAULT TRUE,
    "creator" varchar(64) DEFAULT '',
    "create_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updater" varchar(64) DEFAULT '',
    "update_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted" bit NOT NULL DEFAULT FALSE,
    PRIMARY KEY ("id")
) COMMENT '菜单权限表';
```

**字段说明：**
- `id`: 菜单唯一标识
- `name`: 菜单名称
- `permission`: 权限标识
- `type`: 菜单类型（1目录 2菜单 3按钮）
- `sort`: 显示顺序
- `parent_id`: 父菜单ID
- `path`: 路由地址
- `icon`: 菜单图标
- `component`: 组件路径
- `component_name`: 组件名称
- `status`: 菜单状态（0禁用1启用）
- `visible`: 是否显示
- `keep_alive`: 是否缓存
- `always_show`: 是否总是显示

### 2.6 角色菜单关联表 (system_role_menu)

**表说明：** 角色和菜单的多对多关联表，实现角色菜单权限分配。

```sql
CREATE TABLE IF NOT EXISTS "system_role_menu" (
    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "role_id" bigint NOT NULL,
    "menu_id" bigint NOT NULL,
    "creator" varchar(64) DEFAULT '',
    "create_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updater" varchar(64) DEFAULT '',
    "update_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted" bit NOT NULL DEFAULT FALSE,
    "tenant_id" bigint not null default  '0',
    PRIMARY KEY ("id")
) COMMENT '角色和菜单关联表';
```

**字段说明：**
- `id`: 关联记录唯一标识
- `role_id`: 角色ID
- `menu_id`: 菜单ID
- `tenant_id`: 多租户支持

### 2.7 岗位信息表 (system_post)

**表说明：** 岗位信息管理表，用于管理组织架构中的岗位信息。

```sql
CREATE TABLE IF NOT EXISTS "system_post" (
    "id"          bigint      NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "code"        varchar(64) NOT NULL,
    "name"        varchar(50) NOT NULL,
    "sort"        integer     NOT NULL,
    "status"      tinyint     NOT NULL,
    "remark"      varchar(500)         DEFAULT NULL,
    "creator"     varchar(64)          DEFAULT '',
    "create_time" timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updater"     varchar(64)          DEFAULT '',
    "update_time" timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted"     bit         NOT NULL DEFAULT FALSE,
    "tenant_id" bigint not null default  '0',
    PRIMARY KEY ("id")
) COMMENT '岗位信息表';
```

**字段说明：**
- `id`: 岗位唯一标识
- `code`: 岗位编码
- `name`: 岗位名称
- `sort`: 显示顺序
- `status`: 岗位状态（0禁用1启用）
- `remark`: 备注
- `tenant_id`: 多租户支持

### 2.8 用户会话表 (system_user_session)

**表说明：** 用户在线会话管理表，用于跟踪用户登录状态和会话信息。

```sql
CREATE TABLE IF NOT EXISTS `system_user_session` (
    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    `token` varchar(32) NOT NULL,
    `user_id` bigint DEFAULT NULL,
    "user_type" tinyint NOT NULL,
    `username` varchar(50) NOT NULL DEFAULT '',
    `user_ip` varchar(50) DEFAULT NULL,
    `user_agent` varchar(512) DEFAULT NULL,
    `session_timeout` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "creator" varchar(64) DEFAULT '',
    "create_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updater` varchar(64) DEFAULT '' ,
    "update_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted" bit NOT NULL DEFAULT FALSE,
    "tenant_id" bigint not null default  '0',
    PRIMARY KEY (`id`)
) COMMENT '用户在线 Session';
```

**字段说明：**
- `id`: 会话记录唯一标识
- `token`: 会话令牌
- `user_id`: 用户ID
- `user_type`: 用户类型
- `username`: 用户名
- `user_ip`: 用户IP地址
- `user_agent`: 浏览器信息
- `session_timeout`: 会话超时时间
- `tenant_id`: 多租户支持

---

## 基础支撑模块数据库表

### 3.1 文件管理表 (infra_file)

**表说明：** 文件存储管理表，用于管理系统中的文件上传、下载和存储。

```sql
CREATE TABLE IF NOT EXISTS "infra_file" (
    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "config_id" bigint NOT NULL,
    "name" varchar(256),
    "path" varchar(512),
    "url" varchar(1024),
    "type" varchar(63) DEFAULT NULL,
    "size" bigint NOT NULL,
    "creator" varchar(64) DEFAULT '',
    "create_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updater" varchar(64) DEFAULT '',
    "update_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted" bit NOT NULL DEFAULT FALSE,
    "tenant_id" bigint not null default  '0',
    PRIMARY KEY ("id")
) COMMENT '文件表';
```

**字段说明：**
- `id`: 文件唯一标识
- `config_id`: 文件配置ID
- `name`: 文件名称
- `path`: 文件存储路径
- `url`: 文件访问URL
- `type`: 文件类型
- `size`: 文件大小
- `tenant_id`: 多租户支持

### 3.2 API访问日志表 (infra_api_access_log)

**表说明：** API访问日志记录表，用于系统监控和审计。

```sql
CREATE TABLE `infra_api_access_log`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `trace_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '链路追踪编号',
  `user_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '用户编号',
  `user_type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '用户类型',
  `application_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '应用名',
  `request_method` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '请求方法名',
  `request_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '请求地址',
  `request_params` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '请求参数',
  `response_body` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '响应结果',
  `user_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户 IP',
  `user_agent` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '浏览器 UA',
  `operate_module` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '操作模块',
  `operate_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '操作名',
  `operate_type` tinyint(4) NULL DEFAULT 0 COMMENT '操作分类',
  `begin_time` datetime NOT NULL COMMENT '开始请求时间',
  `end_time` datetime NOT NULL COMMENT '结束请求时间',
  `duration` int(11) NOT NULL COMMENT '执行时长',
  `result_code` int(11) NOT NULL DEFAULT 0 COMMENT '结果码',
  `result_msg` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '结果提示',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_create_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'API 访问日志表' ROW_FORMAT = DYNAMIC;
```

**字段说明：**
- `id`: 日志主键
- `trace_id`: 链路追踪编号
- `user_id`: 操作用户ID
- `application_name`: 应用名称
- `request_method`: HTTP请求方法
- `request_url`: 请求URL
- `request_params`: 请求参数
- `response_body`: 响应内容
- `user_ip`: 用户IP地址
- `operate_module`: 操作模块
- `operate_name`: 操作名称
- `begin_time`: 请求开始时间
- `end_time`: 请求结束时间
- `duration`: 执行时长
- `result_code`: 响应状态码
- `tenant_id`: 多租户支持

### 3.3 API错误日志表 (infra_api_error_log)

**表说明：** API错误日志记录表，用于记录系统运行过程中的异常和错误信息。

```sql
CREATE TABLE `infra_api_error_log`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `trace_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '链路追踪编号',
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户编号',
  `user_type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '用户类型',
  `application_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '应用名',
  `request_method` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '请求方法名',
  `request_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '请求地址',
  `request_params` varchar(8000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '请求参数',
  `user_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户 IP',
  `user_agent` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '浏览器 UA',
  `exception_time` datetime NOT NULL COMMENT '异常发生时间',
  `exception_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '异常名',
  `exception_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '异常导致的消息',
  `exception_root_cause_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '异常导致的根消息',
  `exception_stack_trace` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '异常的栈轨迹',
  `exception_class_name` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '异常发生的类全名',
  `exception_file_name` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '异常发生的类文件',
  `exception_method_name` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '异常发生的方法名',
  `exception_line_number` int(11) NOT NULL COMMENT '异常发生的方法所在行',
  `process_status` tinyint(4) NOT NULL COMMENT '处理状态',
  `process_time` datetime NULL DEFAULT NULL COMMENT '处理时间',
  `process_user_id` int(11) NULL DEFAULT 0 COMMENT '处理用户编号',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '系统异常日志' ROW_FORMAT = DYNAMIC;
```

**字段说明：**
- `id`: 错误日志主键
- `trace_id`: 链路追踪编号
- `user_id`: 操作用户ID
- `application_name`: 应用名称
- `request_method`: HTTP请求方法
- `request_url`: 请求URL
- `request_params`: 请求参数
- `user_ip`: 用户IP地址
- `exception_time`: 异常发生时间
- `exception_name`: 异常名称
- `exception_message`: 异常消息
- `exception_stack_trace`: 异常堆栈信息
- `process_status`: 处理状态
- `tenant_id`: 多租户支持

### 3.4 多租户表 (system_tenant)

**表说明：** 多租户管理表，支持SaaS模式的多租户架构。

```sql
CREATE TABLE IF NOT EXISTS "system_tenant" (
    "id" bigint NOT NULL GENERATED BY DEFAULT AS IDENTITY,
    "name" varchar(63) NOT NULL,
    "contact_user_id" bigint NOT NULL DEFAULT '0',
    "contact_name" varchar(255) NOT NULL,
    "contact_mobile" varchar(255),
    "status" tinyint NOT NULL,
    "website" varchar(63) DEFAULT '',
    "package_id"  bigint NOT NULL,
    "expire_time" timestamp NOT NULL,
    "account_count" int NOT NULL,
    "creator" varchar(64) DEFAULT '',
    "create_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updater" varchar(64) DEFAULT '',
    "update_time" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "deleted" bit NOT NULL DEFAULT FALSE,
    PRIMARY KEY ("id")
) COMMENT '租户';
```

**字段说明：**
- `id`: 租户唯一标识
- `name`: 租户名称
- `contact_user_id`: 联系人用户ID
- `contact_name`: 联系人姓名
- `contact_mobile`: 联系人手机号
- `status`: 租户状态（0禁用1启用）
- `website`: 租户网站
- `package_id`: 租户套餐ID
- `expire_time`: 过期时间
- `account_count`: 账号数量

---

## 数据库表关系图

基于以上表结构，系统的核心表关系如下：

```
数字画像系统数据库表关系：

核心业务模块：
digital_projects (项目表) ←→ system_users (用户表)
digital_competition (竞赛表) ←→ system_users (用户表)

系统管理模块：
system_users (用户表) ←→ system_dept (部门表)
system_users (用户表) ←→ system_user_role (用户角色关联表) ←→ system_role (角色表)
system_role (角色表) ←→ system_role_menu (角色菜单关联表) ←→ system_menu (菜单表)
system_users (用户表) ←→ system_user_post (用户岗位关联表) ←→ system_post (岗位表)

基础支撑模块：
infra_file (文件表) ←→ digital_projects/digital_competition (佐证材料)
infra_api_access_log (访问日志表) ←→ system_users (用户表)
infra_api_error_log (错误日志表) ←→ system_users (用户表)

多租户支持：
system_tenant (租户表) ←→ 所有业务表 (通过tenant_id字段)
```

---

## 总结

本文档整理了数字画像系统中与技术论文相对应的主要数据库表结构，涵盖了：

### 核心业务模块（2个表）
- **digital_projects（项目表）**：存储教师参与的科研项目和教学项目信息
- **digital_competition（竞赛表）**：记录教师指导学生参加各类学科竞赛的情况

### 系统管理模块（8个表）
- **system_users（用户表）**：系统用户基础信息管理
- **system_dept（部门表）**：支持多级部门结构管理
- **system_role（角色表）**：角色权限管理，实现RBAC模型
- **system_user_role（用户角色关联表）**：用户和角色的多对多关联
- **system_menu（菜单权限表）**：系统菜单和权限控制
- **system_role_menu（角色菜单关联表）**：角色和菜单的多对多关联
- **system_post（岗位表）**：组织架构中的岗位信息管理
- **system_user_session（用户会话表）**：用户在线状态和会话管理

### 基础支撑模块（4个表）
- **infra_file（文件表）**：文件上传、下载和存储管理
- **infra_api_access_log（API访问日志表）**：系统访问日志记录
- **infra_api_error_log（API错误日志表）**：系统异常和错误日志
- **system_tenant（租户表）**：多租户架构支持

### 设计特点
1. **统一设计规范**：所有表都采用统一的字段命名和数据类型规范
2. **多租户支持**：通过tenant_id字段实现数据隔离，支持SaaS模式
3. **软删除机制**：通过deleted字段实现逻辑删除，保证数据安全
4. **审计字段**：包含creator、create_time、updater、update_time等审计字段
5. **权限控制**：基于RBAC模型实现细粒度的权限管理
6. **扩展性设计**：预留reserved字段，便于后续功能扩展

### 技术特性
- **数据库引擎**：使用InnoDB引擎，支持事务和外键约束
- **字符集**：采用utf8mb4字符集，支持完整的Unicode字符
- **索引优化**：在关键字段上建立索引，提升查询性能
- **数据完整性**：通过外键约束和检查约束保证数据完整性

该数据库设计为数字画像系统的稳定运行提供了坚实的数据基础，完全支持技术论文中描述的五个核心功能模块：项目管理、竞赛管理、用户管理、部门管理和角色管理。
