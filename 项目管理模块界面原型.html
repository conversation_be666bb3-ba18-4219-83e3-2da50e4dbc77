<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目管理模块 - 数字画像系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }
        
        .container {
            display: flex;
            min-height: 100vh;
        }
        
        /* 侧边栏样式 */
        .sidebar {
            width: 240px;
            background: #2c3e50;
            color: white;
            padding: 20px 0;
        }
        
        .logo {
            text-align: center;
            padding: 20px;
            border-bottom: 1px solid #34495e;
            margin-bottom: 20px;
        }
        
        .logo h2 {
            color: #3498db;
        }
        
        .menu-item {
            padding: 12px 20px;
            cursor: pointer;
            transition: background 0.3s;
            border-left: 3px solid transparent;
        }
        
        .menu-item:hover {
            background: #34495e;
        }
        
        .menu-item.active {
            background: #3498db;
            border-left-color: #2980b9;
        }
        
        .menu-item i {
            margin-right: 10px;
            width: 16px;
        }
        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            justify-content: between;
            align-items: center;
        }
        
        .breadcrumb {
            color: #666;
            font-size: 14px;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        /* 搜索区域 */
        .search-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .search-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group label {
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }
        
        .form-group input,
        .form-group select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-success {
            background: #27ae60;
            color: white;
        }
        
        .btn-warning {
            background: #f39c12;
            color: white;
        }
        
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        
        .btn:hover {
            opacity: 0.8;
            transform: translateY(-1px);
        }
        
        /* 工具栏 */
        .toolbar {
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            gap: 10px;
        }
        
        /* 表格样式 */
        .table-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .table-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            font-weight: 600;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #555;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .status-tag {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-ongoing {
            background: #e3f2fd;
            color: #1976d2;
        }
        
        .status-completed {
            background: #e8f5e8;
            color: #2e7d32;
        }
        
        .status-pending {
            background: #fff3e0;
            color: #f57c00;
        }
        
        .level-tag {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .level-national {
            background: #ffebee;
            color: #c62828;
        }
        
        .level-provincial {
            background: #e8f5e8;
            color: #2e7d32;
        }
        
        .level-school {
            background: #e3f2fd;
            color: #1976d2;
        }
        
        .action-buttons {
            display: flex;
            gap: 5px;
        }
        
        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
        }
        
        /* 分页 */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            gap: 10px;
        }
        
        .page-btn {
            padding: 6px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.3s;
        }
        
        .page-btn:hover {
            background: #f5f5f5;
        }
        
        .page-btn.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }
        
        .checkbox {
            width: 16px;
            height: 16px;
        }
        
        .progress-bar {
            width: 100px;
            height: 6px;
            background: #eee;
            border-radius: 3px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: #3498db;
            transition: width 0.3s;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="logo">
                <h2>数字画像系统</h2>
            </div>
            <div class="menu-item active">
                <i>📊</i> 项目管理
            </div>
            <div class="menu-item">
                <i>🏆</i> 竞赛管理
            </div>
            <div class="menu-item">
                <i>👥</i> 用户管理
            </div>
            <div class="menu-item">
                <i>🏢</i> 部门管理
            </div>
            <div class="menu-item">
                <i>🔐</i> 角色管理
            </div>
            <div class="menu-item">
                <i>📈</i> 数据分析
            </div>
            <div class="menu-item">
                <i>⚙️</i> 系统设置
            </div>
        </div>
        
        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 页面头部 -->
            <div class="header">
                <div class="breadcrumb">
                    首页 > 项目管理 > 项目列表
                </div>
                <div class="user-info">
                    <span>欢迎，张教授</span>
                    <button class="btn btn-primary btn-small">退出</button>
                </div>
            </div>
            
            <!-- 搜索区域 -->
            <div class="search-section">
                <div class="search-form">
                    <div class="form-group">
                        <label>项目名称</label>
                        <input type="text" placeholder="请输入项目名称">
                    </div>
                    <div class="form-group">
                        <label>项目类型</label>
                        <select>
                            <option value="">全部类型</option>
                            <option value="1">科研项目</option>
                            <option value="2">教学项目</option>
                            <option value="3">产学研项目</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>项目级别</label>
                        <select>
                            <option value="">全部级别</option>
                            <option value="1">国家级</option>
                            <option value="2">省级</option>
                            <option value="3">校级</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>项目状态</label>
                        <select>
                            <option value="">全部状态</option>
                            <option value="1">立项</option>
                            <option value="2">进行中</option>
                            <option value="3">结项</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <div style="display: flex; gap: 10px;">
                            <button class="btn btn-primary">查询</button>
                            <button class="btn">重置</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 工具栏 -->
            <div class="toolbar">
                <button class="btn btn-success">新增项目</button>
                <button class="btn btn-warning">批量导入</button>
                <button class="btn btn-primary">导出数据</button>
                <button class="btn btn-danger">批量删除</button>
            </div>
            
            <!-- 数据表格 -->
            <div class="table-container">
                <div class="table-header">
                    项目列表 (共 25 条记录)
                </div>
                <table>
                    <thead>
                        <tr>
                            <th><input type="checkbox" class="checkbox"></th>
                            <th>项目编号</th>
                            <th>项目名称</th>
                            <th>项目类型</th>
                            <th>项目级别</th>
                            <th>负责人</th>
                            <th>进度</th>
                            <th>立项时间</th>
                            <th>结项时间</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><input type="checkbox" class="checkbox"></td>
                            <td>1234</td>
                            <td>基于深度学习的图像识别技术研究</td>
                            <td>科研项目</td>
                            <td><span class="level-tag level-national">国家级</span></td>
                            <td>张教授</td>
                            <td>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 75%"></div>
                                </div>
                                75%
                            </td>
                            <td>2023-01-08</td>
                            <td>2025-01-24</td>
                            <td><span class="status-tag status-ongoing">进行中</span></td>
                            <td>2025-01-19 02:16:11</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn btn-primary btn-small">编辑</button>
                                    <button class="btn btn-warning btn-small">详情</button>
                                    <button class="btn btn-danger btn-small">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="checkbox"></td>
                            <td>5678</td>
                            <td>新工科背景下的计算机教学改革研究</td>
                            <td>教学项目</td>
                            <td><span class="level-tag level-provincial">省级</span></td>
                            <td>李教授</td>
                            <td>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 100%"></div>
                                </div>
                                100%
                            </td>
                            <td>2022-03-20</td>
                            <td>2024-03-20</td>
                            <td><span class="status-tag status-completed">已结项</span></td>
                            <td>2025-01-18 10:45:33</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn btn-primary btn-small">编辑</button>
                                    <button class="btn btn-warning btn-small">详情</button>
                                    <button class="btn btn-danger btn-small">删除</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="checkbox"></td>
                            <td>9012</td>
                            <td>智能制造产学研合作项目</td>
                            <td>产学研项目</td>
                            <td><span class="level-tag level-school">校级</span></td>
                            <td>王教授</td>
                            <td>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 30%"></div>
                                </div>
                                30%
                            </td>
                            <td>2024-06-01</td>
                            <td>2026-06-01</td>
                            <td><span class="status-tag status-pending">立项</span></td>
                            <td>2025-01-18 16:22:15</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn btn-primary btn-small">编辑</button>
                                    <button class="btn btn-warning btn-small">详情</button>
                                    <button class="btn btn-danger btn-small">删除</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
                
                <!-- 分页 -->
                <div class="pagination">
                    <button class="page-btn">上一页</button>
                    <button class="page-btn active">1</button>
                    <button class="page-btn">2</button>
                    <button class="page-btn">3</button>
                    <button class="page-btn">下一页</button>
                    <span style="margin-left: 20px;">共 25 条记录，每页 10 条</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
