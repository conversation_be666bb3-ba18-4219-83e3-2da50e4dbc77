<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>角色管理模块 - 数字画像系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background-color: #f5f7fa;
            color: #333;
        }
        
        .container {
            display: flex;
            min-height: 100vh;
        }
        
        /* 侧边栏样式 */
        .sidebar {
            width: 240px;
            background: #2c3e50;
            color: white;
            padding: 20px 0;
        }
        
        .logo {
            text-align: center;
            padding: 20px;
            border-bottom: 1px solid #34495e;
            margin-bottom: 20px;
        }
        
        .logo h2 {
            color: #3498db;
        }
        
        .menu-item {
            padding: 12px 20px;
            cursor: pointer;
            transition: background 0.3s;
            border-left: 3px solid transparent;
        }
        
        .menu-item:hover {
            background: #34495e;
        }
        
        .menu-item.active {
            background: #3498db;
            border-left-color: #2980b9;
        }
        
        .menu-item i {
            margin-right: 10px;
            width: 16px;
        }
        
        /* 主内容区域 */
        .main-content {
            flex: 1;
            padding: 20px;
        }
        
        .header {
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .breadcrumb {
            color: #666;
            font-size: 14px;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        /* 搜索区域 */
        .search-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .search-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            align-items: end;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-group label {
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }
        
        .form-group input,
        .form-group select {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: #3498db;
            color: white;
        }
        
        .btn-success {
            background: #27ae60;
            color: white;
        }
        
        .btn-warning {
            background: #f39c12;
            color: white;
        }
        
        .btn-danger {
            background: #e74c3c;
            color: white;
        }
        
        .btn:hover {
            opacity: 0.8;
            transform: translateY(-1px);
        }
        
        /* 工具栏 */
        .toolbar {
            background: white;
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            display: flex;
            gap: 10px;
        }
        
        /* 表格样式 */
        .table-container {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .table-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #eee;
            font-weight: 600;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #eee;
        }
        
        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #555;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .status-tag {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-active {
            background: #e8f5e8;
            color: #2e7d32;
        }
        
        .status-inactive {
            background: #ffebee;
            color: #c62828;
        }
        
        .permission-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
            max-width: 200px;
        }
        
        .permission-tag {
            padding: 2px 6px;
            background: #e3f2fd;
            color: #1976d2;
            border-radius: 4px;
            font-size: 11px;
        }
        
        .data-scope-tag {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .scope-all {
            background: #ffebee;
            color: #c62828;
        }
        
        .scope-dept {
            background: #e8f5e8;
            color: #2e7d32;
        }
        
        .scope-self {
            background: #fff3e0;
            color: #f57c00;
        }
        
        .action-buttons {
            display: flex;
            gap: 5px;
        }
        
        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
        }
        
        /* 分页 */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            gap: 10px;
        }
        
        .page-btn {
            padding: 6px 12px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.3s;
        }
        
        .page-btn:hover {
            background: #f5f5f5;
        }
        
        .page-btn.active {
            background: #3498db;
            color: white;
            border-color: #3498db;
        }
        
        .checkbox {
            width: 16px;
            height: 16px;
        }
        
        .role-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #3498db;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 14px;
        }
        
        .role-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .role-details {
            display: flex;
            flex-direction: column;
        }
        
        .role-name {
            font-weight: 500;
            color: #333;
        }
        
        .role-key {
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <div class="logo">
                <h2>数字画像系统</h2>
            </div>
            <div class="menu-item">
                <i>📊</i> 项目管理
            </div>
            <div class="menu-item">
                <i>🏆</i> 竞赛管理
            </div>
            <div class="menu-item">
                <i>👥</i> 用户管理
            </div>
            <div class="menu-item">
                <i>🏢</i> 部门管理
            </div>
            <div class="menu-item active">
                <i>🔐</i> 角色管理
            </div>
            <div class="menu-item">
                <i>📈</i> 数据分析
            </div>
            <div class="menu-item">
                <i>⚙️</i> 系统设置
            </div>
        </div>
        
        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 页面头部 -->
            <div class="header">
                <div class="breadcrumb">
                    首页 > 系统管理 > 角色管理
                </div>
                <div class="user-info">
                    <span>欢迎，管理员</span>
                    <button class="btn btn-primary btn-small">退出</button>
                </div>
            </div>
            
            <!-- 搜索区域 -->
            <div class="search-section">
                <div class="search-form">
                    <div class="form-group">
                        <label>角色名称</label>
                        <input type="text" placeholder="请输入角色名称">
                    </div>
                    <div class="form-group">
                        <label>角色标识</label>
                        <input type="text" placeholder="请输入角色标识">
                    </div>
                    <div class="form-group">
                        <label>角色状态</label>
                        <select>
                            <option value="">全部状态</option>
                            <option value="1">启用</option>
                            <option value="0">禁用</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>创建时间</label>
                        <input type="date">
                    </div>
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <div style="display: flex; gap: 10px;">
                            <button class="btn btn-primary">查询</button>
                            <button class="btn">重置</button>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 工具栏 -->
            <div class="toolbar">
                <button class="btn btn-success">新增角色</button>
                <button class="btn btn-primary">导出数据</button>
                <button class="btn btn-danger">批量删除</button>
            </div>
            
            <!-- 数据表格 -->
            <div class="table-container">
                <div class="table-header">
                    角色列表 (共 8 条记录)
                </div>
                <table>
                    <thead>
                        <tr>
                            <th><input type="checkbox" class="checkbox"></th>
                            <th>角色信息</th>
                            <th>权限范围</th>
                            <th>数据权限</th>
                            <th>菜单权限</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><input type="checkbox" class="checkbox"></td>
                            <td>
                                <div class="role-info">
                                    <div class="role-icon">管</div>
                                    <div class="role-details">
                                        <div class="role-name">系统管理员</div>
                                        <div class="role-key">admin</div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="permission-tags">
                                    <span class="permission-tag">用户管理</span>
                                    <span class="permission-tag">角色管理</span>
                                    <span class="permission-tag">部门管理</span>
                                    <span class="permission-tag">系统设置</span>
                                    <span class="permission-tag">+5</span>
                                </div>
                            </td>
                            <td><span class="data-scope-tag scope-all">全部数据</span></td>
                            <td>
                                <div class="permission-tags">
                                    <span class="permission-tag">项目管理</span>
                                    <span class="permission-tag">竞赛管理</span>
                                    <span class="permission-tag">系统管理</span>
                                    <span class="permission-tag">+3</span>
                                </div>
                            </td>
                            <td><span class="status-tag status-active">启用</span></td>
                            <td>2023-08-01 09:00:00</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn btn-primary btn-small">编辑</button>
                                    <button class="btn btn-warning btn-small">权限</button>
                                    <button class="btn btn-danger btn-small">禁用</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="checkbox"></td>
                            <td>
                                <div class="role-info">
                                    <div class="role-icon">教</div>
                                    <div class="role-details">
                                        <div class="role-name">教师</div>
                                        <div class="role-key">teacher</div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="permission-tags">
                                    <span class="permission-tag">项目管理</span>
                                    <span class="permission-tag">竞赛管理</span>
                                    <span class="permission-tag">个人中心</span>
                                </div>
                            </td>
                            <td><span class="data-scope-tag scope-self">个人数据</span></td>
                            <td>
                                <div class="permission-tags">
                                    <span class="permission-tag">项目管理</span>
                                    <span class="permission-tag">竞赛管理</span>
                                    <span class="permission-tag">数据分析</span>
                                </div>
                            </td>
                            <td><span class="status-tag status-active">启用</span></td>
                            <td>2023-08-01 09:00:00</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn btn-primary btn-small">编辑</button>
                                    <button class="btn btn-warning btn-small">权限</button>
                                    <button class="btn btn-danger btn-small">禁用</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="checkbox"></td>
                            <td>
                                <div class="role-info">
                                    <div class="role-icon">院</div>
                                    <div class="role-details">
                                        <div class="role-name">院系管理员</div>
                                        <div class="role-key">dept_admin</div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="permission-tags">
                                    <span class="permission-tag">用户管理</span>
                                    <span class="permission-tag">项目管理</span>
                                    <span class="permission-tag">竞赛管理</span>
                                    <span class="permission-tag">数据统计</span>
                                </div>
                            </td>
                            <td><span class="data-scope-tag scope-dept">本部门数据</span></td>
                            <td>
                                <div class="permission-tags">
                                    <span class="permission-tag">项目管理</span>
                                    <span class="permission-tag">竞赛管理</span>
                                    <span class="permission-tag">用户管理</span>
                                    <span class="permission-tag">+2</span>
                                </div>
                            </td>
                            <td><span class="status-tag status-active">启用</span></td>
                            <td>2023-08-01 09:00:00</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn btn-primary btn-small">编辑</button>
                                    <button class="btn btn-warning btn-small">权限</button>
                                    <button class="btn btn-danger btn-small">禁用</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><input type="checkbox" class="checkbox"></td>
                            <td>
                                <div class="role-info">
                                    <div class="role-icon">学</div>
                                    <div class="role-details">
                                        <div class="role-name">学生</div>
                                        <div class="role-key">student</div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="permission-tags">
                                    <span class="permission-tag">个人中心</span>
                                    <span class="permission-tag">竞赛查看</span>
                                </div>
                            </td>
                            <td><span class="data-scope-tag scope-self">个人数据</span></td>
                            <td>
                                <div class="permission-tags">
                                    <span class="permission-tag">个人中心</span>
                                    <span class="permission-tag">竞赛查看</span>
                                </div>
                            </td>
                            <td><span class="status-tag status-inactive">禁用</span></td>
                            <td>2023-08-01 09:00:00</td>
                            <td>
                                <div class="action-buttons">
                                    <button class="btn btn-primary btn-small">编辑</button>
                                    <button class="btn btn-warning btn-small">权限</button>
                                    <button class="btn btn-success btn-small">启用</button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
                
                <!-- 分页 -->
                <div class="pagination">
                    <button class="page-btn">上一页</button>
                    <button class="page-btn active">1</button>
                    <button class="page-btn">2</button>
                    <button class="page-btn">下一页</button>
                    <span style="margin-left: 20px;">共 8 条记录，每页 10 条</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
